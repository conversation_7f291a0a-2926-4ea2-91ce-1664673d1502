from decimal import Decimal as D
from rede.types import D as DD
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from .helpers import run_engine

def test_s2_uc_letout_vacancy_194ib():
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(800000))
    prop = Property(
        id="INV1", city="Mumbai",
        heads=PropertyHeads(base_price_ex_gst=DD(10_000_000), tds_buy_pct=DD(1)),
        plan=PaymentPlan(events=[]),  # assume already acquired / or zero for simplicity
        ownership_costs=PropertyOwnershipCosts(annual_property_tax=DD(12000), maintenance_monthly=DD(5000)),
        occupancy=PropertyOccupancy(
            let_out_month=3, move_in_month=None,
            rent_yield_pct_of_value=DD(3.0), vacancy_pct=DD(6), collection_efficiency_pct=DD(98),
            pm_fee_monthly=DD(0), deposit_months=DD(2)
        )
    )
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=24,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(800000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )
    res = run_engine(pf)
    ledger = res["ledger"]
    # Expect rent received entries from let_out_month onward
    assert any(l for l in ledger if l["category"] == "rent_received"), "Expected rent received entries"
    # Expect TDS credits KPI present (may be zero if threshold not met)
    kpis = res["kpis"]
    assert "tds_credits_accumulated" in kpis
    # Tax panel should exist
    assert res.get("tax_panel") is not None, "Expected tax_panel in results"
