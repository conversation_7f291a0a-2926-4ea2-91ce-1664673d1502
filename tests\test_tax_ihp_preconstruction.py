from decimal import Decimal as D

from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.types import D as DD


def _ctx_tax_only():
    class _Cfg:
        content = {
            "tds_rules": {"rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}},
            "mf_tax": {"equity": {"holding_days_ltcg": 365, "stcg_rate_pct": 15, "ltcg_rate_pct": 10,
                                  "ltcg_annual_exemption": 100000, "indexation_allowed": False}},
            "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
            "cii": {"FY2024-25": 348, "FY2025-26": 360}
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx


def _portfolio_for_rent(value_base: D, yield_pct: D):
    p = Person(id="p1", name="Owner", tax_regime="old", shareholdings={}, liquid_cash=DD(0))
    prop = Property(
        id="propRent", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=value_base),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(let_out_month=0, rent_yield_pct_of_value=yield_pct)
    )
    return Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=2,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"p1": DD(0)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )


def test_rent_tds_threshold_edge_above_and_below():
    ctx = _ctx_tax_only()

    # Scenario A: just BELOW 50k monthly (e.g., 1.8 Cr @ 3% p.a. => 45,000/m)
    pf_a = _portfolio_for_rent(value_base=DD(18_000_000), yield_pct=DD(3.0))
    eng_a = TimelineEngine(ctx); eng_a.ctx.portfolio = pf_a
    eng_a.run()
    res_a = eng_a.result()
    tax_panel_a = res_a.get("tax_panel", {})
    # Extract FY panel (engine aggregates per FY; our FY is 2025-26)
    rent_tds_credit_a = float(tax_panel_a.get("rent_tds_credit", 0.0))
    assert rent_tds_credit_a == 0.0, f"Expected no TDS credit when monthly rent < 50k, got {rent_tds_credit_a}"

    # Scenario B: just ABOVE 50k monthly (e.g., 2.4 Cr @ 3% p.a. => 60,000/m)
    pf_b = _portfolio_for_rent(value_base=DD(24_000_000), yield_pct=DD(3.0))
    eng_b = TimelineEngine(ctx); eng_b.ctx.portfolio = pf_b
    eng_b.run()
    res_b = eng_b.result()
    tax_panel_b = res_b.get("tax_panel", {})
    rent_tds_credit_b = float(tax_panel_b.get("rent_tds_credit", 0.0))
    assert rent_tds_credit_b > 0.0, "Expected TDS credit when monthly rent > 50k"
