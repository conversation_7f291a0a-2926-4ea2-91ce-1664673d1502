import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger,
    PropertyOwnershipCosts, PropertyOccupancy, ExitSettings
)
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD

def _ctx_with_sec54():
    """
    Build a context and tax config that *may* include sec54 settings.
    If your build doesn't support it yet, the test will skip gracefully.
    """
    class _Cfg:
        content = {
            # Minimal CII / CGT baseline; add sec54/54F knobs if wired
            "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
            "cii": {"FY2024-25": 348, "FY2025-26": 360},
            # Optional feature flags (engine may ignore if unsupported)
            "sec54": {"enabled": True, "purchase_window_months": 24, "construction_window_months": 36}
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def _build_portfolio(enable_rollover: bool):
    # Seller property (A): buy at m0, sell at m12
    buy_A = PaymentEvent(
        name="Buy A", stage="SaleDeed",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
        percent_of_base=DD(100), gst_pct=DD(0), financeable_by_bank=False
    )
    A = Property(
        id="A", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(8_000_000)),
        plan=PaymentPlan(events=[buy_A]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        exit=ExitSettings(sale_month=12, brokerage_pct=DD(1.0), buyer_tds_pct=DD(1.0))
    )

    # New residential purchase (B) at m14 – used to claim rollover if supported
    buy_B = PaymentEvent(
        name="Buy B", stage="SaleDeed",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=14),
        percent_of_base=DD(100), gst_pct=DD(0), financeable_by_bank=False
    )
    B = Property(
        id="B", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(10_000_000)),
        plan=PaymentPlan(events=[buy_B]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(move_in_month=16)
    )

    # Flag rollover on B ↔ A if the model exposes such a knob; otherwise we will skip later
    if enable_rollover:
        if hasattr(B, "rollover_from_property_id"):
            B.rollover_from_property_id = "A"
        elif hasattr(B, "tax_rollover") and isinstance(B.tax_rollover, dict):
            B.tax_rollover["from_property_id"] = "A"
            B.tax_rollover["section"] = "54"
        # If neither field exists, the test will skip after running.

    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(2_000_000))
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=30,
        persons=[p], properties=[A, B], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(2_000_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )
    return pf

def _sum_property_cgt(ledger):
    return sum(float(l["amount"]) for l in ledger if "Property CGT" in l["label"])

def test_sec54_rollover_reduces_cgt_if_supported():
    ctx = _ctx_with_sec54()

    # Build two runs: with and without rollover flag
    pf_no = _build_portfolio(enable_rollover=False)
    pf_yes = _build_portfolio(enable_rollover=True)

    # If our build doesn't expose any rollover flag fields, skip
    B_yes = [p for p in pf_yes.properties if p.id == "B"][0]
    if not (hasattr(B_yes, "rollover_from_property_id") or hasattr(B_yes, "tax_rollover")):
        pytest.skip("Sec54 rollover toggle not available in this build")

    # Run both
    e1 = TimelineEngine(ctx); e1.ctx.portfolio = pf_no; e1.run(); r_no = e1.result()
    e2 = TimelineEngine(ctx); e2.ctx.portfolio = pf_yes; e2.run(); r_yes = e2.result()

    cgt_no = _sum_property_cgt(r_no["ledger"])
    cgt_yes = _sum_property_cgt(r_yes["ledger"])

    # With rollover, CGT should be <= without rollover
    assert cgt_yes <= cgt_no, "CGT should reduce (or become zero) when Sec 54 rollover is applied"
