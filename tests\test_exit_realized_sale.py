def test_realized_sale_with_loan_closure_and_cgt():
    # Trigger sale; assert ledger lines for buyer TDS, brokerage, loan closure, CGT (immediate).
    assert True
from decimal import Decimal as D
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger, PropertyOwnershipCosts,
    PropertyOccupancy, ExitSettings
)
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD


def _ctx_basic_tax():
    class _Cfg:
        content = {
            "tds_rules": {"rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}},
            "mf_tax": {"equity": {"holding_days_ltcg": 365, "stcg_rate_pct": 15, "ltcg_rate_pct": 10,
                                  "ltcg_annual_exemption": 100000, "indexation_allowed": False}},
            "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
            "cii": {"FY2024-25": 348, "FY2025-26": 360}
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx


def test_timeline_executes_sale_and_posts_key_lines():
    ctx = _ctx_basic_tax()
    p = Person(id="p1", name="Owner", tax_regime="old", shareholdings={}, liquid_cash=DD(500000))

    # Pay base at t=0 so we have an acquisition; force a sale at month 6 (simple horizon).
    ev_buy = PaymentEvent(
        name="Buy", stage="Booking",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
        percent_of_base=DD(100), gst_pct=DD(0), financeable_by_bank=False
    )
    prop = Property(
        id="propSale", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(300000)),
        plan=PaymentPlan(events=[ev_buy]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        exit=ExitSettings(sale_month=6, brokerage_pct=DD(1), buyer_tds_pct=DD(1), transfer_fee_fixed=DD(0))
    )

    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"p1": DD(500000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf
    eng.run()
    led = eng.result()["ledger"]

    # Assert presence of sale proceeds (net of buyer TDS) and brokerage cost lines
    assert any("Sale Proceeds (net of buyer TDS)" in l["label"] for l in led), "Expected sale proceeds line"
    assert any(l["label"] == "Brokerage on Sale" for l in led), "Expected brokerage line"
