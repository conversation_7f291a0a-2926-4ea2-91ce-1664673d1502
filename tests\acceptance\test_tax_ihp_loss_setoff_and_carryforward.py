import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.models.loans import TermLoan, RateIndex, RateChangePolicy
from rede.types import D as DD

def _ctx_ihp():
    class _Cfg:
        content = {
            "ihp": {
                "self_occupied_interest_cap_per_person": 200000,
                "hp_loss_setoff_cap_per_person": 200000,
                "carry_forward_years": 8
            },
            "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
            "cii": {"FY2024-25": 348, "FY2025-26": 360, "FY2026-27": 372}
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_ihp_loss_carryforward_then_setoff_next_year():
    """
    Build a let-out with loan where Year 1 has a net HP loss (high interest),
    which is carried forward and partially set off in Year 2 as rent grows.
    Assertions: tax_panel exists with carry-forward > 0 after Y1 and reduced after Y2.
    """
    ctx = _ctx_ihp()

    loan = TermLoan(
        id="L1", sanctioned_amount=DD(4_000_000), tenure_months=240,
        initial_roi_annual_pct=DD(9.0), spread_pct=DD(0.0),
        rate_index=RateIndex.FLOATING, on_rate_change_policy=RateChangePolicy.ADJUST_EMI
    )

    prop = Property(
        id="HP1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(10_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(annual_property_tax=DD(15000), maintenance_monthly=DD(3000)),
        occupancy=PropertyOccupancy(let_out_month=0, rent_yield_pct_of_value=DD(2.0), rent_escalation_annual_pct=DD(8)),
        loan=loan
    )
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(300_000))
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=30,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(300_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
    tax = eng.result().get("tax_panel")
    if not tax or "ihp" not in tax:
        pytest.skip("IHP tax panel not available in this build")
    ihp = tax["ihp"]

    # Expect carry-forward balance present after Year 1 (FY 2025-26)
    cf1 = ihp.get("loss_carry_forward_balance_FY2025-26")
    if cf1 is None:
        pytest.skip("Carry-forward balance keys not exposed; skipping structural check")
    assert cf1 > 0, "Expected HP loss carry-forward after Year 1"

    # And reduced (or zero) by end of Year 2 if rent escalates and/or interest drops
    cf2 = ihp.get("loss_carry_forward_balance_FY2026-27")
    assert cf2 is not None, "Expected Year 2 carry-forward key"
    assert cf2 <= cf1, "Carry-forward should not increase if set-off is applied in Year 2"
