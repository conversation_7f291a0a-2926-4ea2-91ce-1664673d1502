from pydantic import BaseModel, Field
from typing import Literal
from ..types import Money, Percent, D
from .enums import TaxRegime

class MFHoldingLot(BaseModel):
    id: str
    scheme_name: str
    asset_type: Literal["equity", "debt"]  # tax treatment differs (handled in TaxEngine)
    units: float
    nav_acquired: float
    acquired_date: str  # ISO date
    current_nav: float
    exit_load_pct: Percent = D(0)  # percent of proceeds

class TaxSettingsGlobal(BaseModel):
    # Keep lean; detailed rates come from tax_laws config loaded in EngineContext
    rent_tds_enabled_default: bool = False

class FundingStrategy(BaseModel):
    waterfall_order: list[str] | None = None
    use_separate_person_pools: bool = False
    od_limit: Money | None = None
    od_annual_rate_pct: Percent = D(12)
    mf_fifo_enabled: bool = True
    allow_hl_topup: bool = False
    # Optional: sweep surplus to OD if loan ROI - alt return >= threshold
    sweep_to_od_threshold_spread_pct: Percent = D(0)

class Person(BaseModel):
    id: str
    name: str
    tax_regime: TaxRegime
    income_profile: dict | None = None
    shareholdings: dict[str, Percent] = Field(default_factory=dict)
    liquid_cash: Money | None = None

class RuntimeConfigs(BaseModel):
    state_rules_path: str
    city_benchmarks_path: str
    tax_laws_path: str
    rate_index_path: str
    bank_policies_path: str

class Portfolio(BaseModel):
    currency: str = "INR"
    start_date: str
    posting_day_of_month: int = Field(default=5, ge=1, le=28)
    horizon_months: int = Field(ge=1)

    persons: list[Person]
    properties: list["Property"]

    # MF lots to enable FIFO redemptions in the funding waterfall
    mf_holdings: list[MFHoldingLot] = Field(default_factory=list)

    user_starting_liquid_cash_by_person: dict[str, Money] | None = None

    # Back-compat: kept for older payloads; superseded by treasury_config.idle_return_after_tax_pct
    alt_invest_return_after_tax_pct: Percent = D(6)

    loan_sanction_risk_pct: Percent = D(10)

    # NEW — Unified Treasury
    from ..models.treasury import TreasuryConfig, TreasuryBucketModel
    treasury_config: "TreasuryConfig" = Field(default_factory=TreasuryConfig)
    treasury_buckets: list["TreasuryBucketModel"] = Field(default_factory=list)

    funding_strategy: FundingStrategy
    tax_settings_global: TaxSettingsGlobal
    configs: RuntimeConfigs


from .property import Property
Portfolio.update_forward_refs()
