from __future__ import annotations
from decimal import Decimal
from ..types import D
from ..models.treasury import TreasuryBucketModel

def preset_liquid() -> TreasuryBucketModel:
    return TreasuryBucketModel(
        id="liquid", name="Liquid", mode="accrual_only",
        opening_balance=D(0), post_tax_return_annual_pct=D(8), liquidity="at_call",
        redeem_priority=10
    )

def preset_fd() -> TreasuryBucketModel:
    return TreasuryBucketModel(
        id="fd_default", name="Fixed Deposit", mode="accrual_only",
        opening_balance=D(0), post_tax_return_annual_pct=D(6.2), liquidity="T+1",
        redeem_priority=20
    )

def preset_commercial_assured() -> TreasuryBucketModel:
    return TreasuryBucketModel(
        id="comm_assured", name="Commercial Assured Deal", mode="accrual_only",
        opening_balance=D(0), post_tax_return_annual_pct=D(10.5), liquidity="locked",
        lock_months=12, coupon_monthly=D(0), redeem_priority=30
    )

def preset_rental_income() -> TreasuryBucketModel:
    return TreasuryBucketModel(
        id="external_rent", name="External Residential Rent", mode="accrual_only",
        opening_balance=D(0), post_tax_return_annual_pct=D(0), liquidity="at_call",
        coupon_monthly=D(0), coupon_escalation_annual_pct=D(5), coupon_sweep="to_liquid",
        redeem_priority=15
    )

def preset_mf_equity_display() -> TreasuryBucketModel:
    # Display-only “tax_tracked” row; accrual = 0; MF redemptions remain in FundingEngine
    return TreasuryBucketModel(
        id="mf_equity", name="Mutual Funds (Equity)", mode="tax_tracked",
        opening_balance=D(0), post_tax_return_annual_pct=D(0), liquidity="T+1",
        asset_type="equity"
    )
