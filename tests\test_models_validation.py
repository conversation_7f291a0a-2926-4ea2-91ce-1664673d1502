import pytest
from pydantic import ValidationError
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.types import D as DD

def test_models_parse_minimal_portfolio():
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(100_000))
    prop = Property(
        id="P1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(0)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    Portfolio.parse_obj({
        "currency":"INR","start_date":"2025-04-05","posting_day_of_month":5,"horizon_months":12,
        "persons":[p.dict()], "properties":[prop.dict()], "mf_holdings":[],
        "user_starting_liquid_cash_by_person":{"u": str(DD(100_000))},
        "alt_invest_return_after_tax_pct": str(DD(6)),
        "loan_sanction_risk_pct": str(DD(0)),
        "funding_strategy": FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False).dict(),
        "tax_settings_global": TaxSettingsGlobal().dict(),
        "configs": RuntimeConfigs("","","","","").dict()
    })

def test_models_validation_errors_on_missing_persons():
    with pytest.raises(ValidationError):
        Portfolio.parse_obj({
            "currency":"INR","start_date":"2025-04-05","posting_day_of_month":5,"horizon_months":12,
            "persons":[], "properties":[], "mf_holdings":[],
            "user_starting_liquid_cash_by_person":{},
            "alt_invest_return_after_tax_pct":"6","loan_sanction_risk_pct":"0",
            "funding_strategy":{"od_limit":"0"}, "tax_settings_global":{}, "configs":{}
        })
