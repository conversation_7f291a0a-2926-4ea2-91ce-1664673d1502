import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.types import D as DD

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_rent_escalation_and_vacancy_if_supported():
    """
    Scenario:
      - Property let-out with base rent = 20k/month
      - Vacancy at m=12-15 (no rent)
      - Escalation +10% every 11 months
    Expect:
      - Ledger shows rent postings skipping vacancy months
      - Escalation reflected post 12m
    """
    ctx = _ctx()

    prop = Property(
        id="REN1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(5_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    person = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(500_000))

    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=36,
        persons=[person], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(500_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    if not hasattr(prop, "rent_schedule"):
        pytest.skip("Rent schedule not wired")

    try:
        prop.rent_schedule = {
            "base_rent": 20000, "escalation_pct": 10, "escalation_every_months": 11,
            "vacancies": [{"start": 12, "end": 15}]
        }
    except Exception:
        pytest.skip("rent_schedule field not assignable")

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
    ledger = eng.result()["ledger"]

    rents = [l for l in ledger if "rent" in l.get("category","").lower()]
    if not rents:
        pytest.skip("No rent postings")
    months = [l["month_index"] for l in rents]
    assert all(m not in months for m in range(12,16)), "Vacancy months should not have rent"
    post12 = [l["amount"] for l in rents if l["month_index"]>12]
    if post12: assert max(post12) > 20000, "Escalation should kick in"
