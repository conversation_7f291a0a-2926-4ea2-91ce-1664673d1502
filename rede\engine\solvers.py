from __future__ import annotations
from typing import Callable, Dict, Any
import copy

CalculateFn = Callable[[Dict[str, Any]], Dict[str, Any]]

def solve_min_liquid_for_no_deficit(base: Dict[str, Any], calc: CalculateFn, *, person_id: str) -> float:
    """Binary search the starting liquid for given person so min_liquid_balance >= 0."""
    lo, hi = 0.0, 5e7  # 5 cr cap; tune
    best = hi
    for _ in range(28):
        mid = (lo + hi) / 2.0
        p = copy.deepcopy(base)
        p.setdefault("user_starting_liquid_cash_by_person", {})[person_id] = mid
        res = calc(p)  # expect to return KPIs incl. min_liquid_balance
        ok = float(res.get("kpis", {}).get("min_liquid_balance", 0.0)) >= 0.0
        if ok:
            best = mid; hi = mid
        else:
            lo = mid
    return best

def solve_sale_month_for_target_xirr(base: Dict[str, Any], calc: CalculateFn, *, prop_index: int, target_xirr: float) -> int:
    """Search sale month that hits a target XIRR (rough). Returns month index or -1."""
    L, R = 3, base.get("horizon_months", 240)
    ans = -1
    while L <= R:
        m = (L + R) // 2
        p = copy.deepcopy(base)
        try:
            p["properties"][prop_index]["exit"]["sale_month"] = m
        except Exception:
            break
        res = calc(p)
        x = res.get("kpis", {}).get("realized_xirr", None)
        if x is None: break
        if x >= target_xirr:
            ans = m; R = m - 1
        else:
            L = m + 1
    return ans
