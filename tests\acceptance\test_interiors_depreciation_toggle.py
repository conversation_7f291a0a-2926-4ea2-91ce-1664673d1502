import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Port<PERSON>lio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.types import D as DD

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_interiors_toggle_changes_mtm_direction():
    """
    Goal: When interiors are excluded from appreciation base and depreciated separately,
    MTM net should be LOWER than when interiors are included in appreciation base.

    We run two portfolios:
      A) Exclude interiors from appreciation base; apply depreciation (if supported)
      B) Include interiors in appreciation base
    Expect: MTM net (A) < MTM net (B)
    """
    ctx = _ctx()

    # Build a minimal property
    prop = Property(
        id="INT-1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(20_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    person = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(0))

    # Check if the build supports interiors config or appreciation_base toggle
    supports_app_base = hasattr(prop, "appreciation_base")
    supports_interiors = hasattr(prop.heads, "interiors_cost") or hasattr(prop.heads, "interior_cost")

    if not (supports_app_base or supports_interiors):
        pytest.skip("Interiors/appr-base toggle not available in this build")

    # A) Exclude interiors from appreciation base
    pfA = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=24,
        persons=[person], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(0)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )
    # Try to set interiors + toggle if attributes exist
    try:
        if hasattr(prop.heads, "interiors_cost"):
            prop.heads.interiors_cost = DD(1_500_000)
        elif hasattr(prop.heads, "interior_cost"):
            prop.heads.interior_cost = DD(1_500_000)
        if hasattr(prop.heads, "interiors_depr_pct"):
            prop.heads.interiors_depr_pct = DD(10)  # 10% p.a.
        if hasattr(prop, "appreciation_base"):
            prop.appreciation_base = ["base_price_ex_gst"]  # exclude interiors by default
    except Exception:
        pytest.skip("Interiors fields present but not assignable in this build")

    engA = TimelineEngine(ctx); engA.ctx.portfolio = pfA; engA.run()
    resA = engA.result(); mtmA = resA["kpis"].get("net_value_today", 0.0)

    # B) Include interiors in appreciation base (if toggle exists)
    prop2 = Property.parse_obj(prop.dict())
    pfB = Portfolio.parse_obj(pfA.dict())
    pfB.properties = [prop2]
    if hasattr(prop2, "appreciation_base"):
        prop2.appreciation_base = ["base_price_ex_gst", "interiors_cost" if hasattr(prop2.heads,"interiors_cost") else "interior_cost"]
    else:
        pytest.skip("Appreciation base toggle not available; cannot run comparative test")

    engB = TimelineEngine(ctx); engB.ctx.portfolio = pfB; engB.run()
    resB = engB.result(); mtmB = resB["kpis"].get("net_value_today", 0.0)

    assert mtmA < mtmB, "Excluding interiors from appreciation base should reduce MTM net vs including them"
