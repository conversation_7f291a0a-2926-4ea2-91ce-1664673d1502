from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.models.loans import TermLoan, RateIndex, RateShock, RateChangePolicy
from rede.types import D as DD

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_rate_shocks_adjust_tenure_multi():
    ctx = _ctx()
    # Simple RTM with a loan so EMI exists and can react to shocks
    loan = TermLoan(
        id="L1", sanctioned_amount=DD(1_000_000), tenure_months=240,
        initial_roi_annual_pct=DD(9.0), spread_pct=DD(0.0),
        rate_index=RateIndex.FLOATING, on_rate_change_policy=RateChangePolicy.ADJUST_TENURE,
        rate_shocks=[RateShock(month=12, new_index_pct=DD(10.0)),
                     RateShock(month=30, new_index_pct=DD(9.5))]
    )
    prop = Property(
        id="RTM1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(1_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        loan=loan
    )
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(200_000))
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=36,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(200_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )
    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf
    eng.run()

    # We don't assert exact EMI/tenure values; just that tenure changed due to shocks
    # and KPIs exist.
    assert eng.ctx.portfolio.properties[0].loan.tenure_months != 240
    assert "mtm_xirr" in eng.result()["kpis"]
