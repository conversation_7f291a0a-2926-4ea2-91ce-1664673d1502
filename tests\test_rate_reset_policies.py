from datetime import date
from decimal import Decimal as D
from rede.engine.loan_engine import <PERSON>an<PERSON><PERSON><PERSON>
from rede.models.loans import Term<PERSON>oan, RateIndex, RateShock, RateChangePolicy
from rede.types import D as DD, Money

def test_adjust_tenure_vs_adjust_emi():
    le = LoanEngine()

    # Base loan with EMI set at t=0
    loan_tenure = TermLoan(
        id="L1", sanctioned_amount=DD(1_000_000), tenure_months=240,
        initial_roi_annual_pct=DD(9.0), spread_pct=DD(0.0),
        rate_index=RateIndex.FLOATING, on_rate_change_policy=RateChangePolicy.ADJUST_TENURE,
        rate_shocks=[RateShock(month=12, new_index_pct=DD(10.0))]
    )
    rm0 = le.monthly_rate(None, loan_tenure, date(2025,4,5), 0)
    emi0 = le.compute_emi(Money(DD(1_000_000)), rm0, 240)
    loan_tenure.current_emi = emi0

    # Apply shock at t=12 → ADJUST_TENURE should change tenure, keep EMI approx same
    le.maybe_apply_rate_shock(loan_tenure, 12, date(2026,4,5), Money(DD(900_000)))
    assert loan_tenure.tenure_months != 240
    assert abs(float(loan_tenure.current_emi) - float(emi0)) < 1.0  # EMI roughly unchanged (rounding tolerance)

    # ADJUST_EMI case
    loan_emi = TermLoan(
        id="L2", sanctioned_amount=DD(1_000_000), tenure_months=240,
        initial_roi_annual_pct=DD(9.0), spread_pct=DD(0.0),
        rate_index=RateIndex.FLOATING, on_rate_change_policy=RateChangePolicy.ADJUST_EMI,
        rate_shocks=[RateShock(month=12, new_index_pct=DD(10.0))]
    )
    rm0b = le.monthly_rate(None, loan_emi, date(2025,4,5), 0)
    emi0b = le.compute_emi(Money(DD(1_000_000)), rm0b, 240)
    loan_emi.current_emi = emi0b

    le.maybe_apply_rate_shock(loan_emi, 12, date(2026,4,5), Money(DD(900_000)))
    assert loan_emi.tenure_months == 240
    assert abs(float(loan_emi.current_emi) - float(emi0b)) > 1.0  # EMI changed
