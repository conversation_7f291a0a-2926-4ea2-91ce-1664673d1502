from __future__ import annotations
from decimal import Decimal
from datetime import date
from typing import List
from ..types import D, Money
from ..engine.ledger import <PERSON><PERSON><PERSON><PERSON>, Ledger
from ..engine.context import EngineContext
from ..utils.money import qrupee
from ..models.treasury import TreasuryBucketModel, TreasuryConfig, TreasuryState
from ..models.core import Portfolio

class TreasuryEngine:
    """
    Unified Treasury:
      - Accrue monthly on accrual-only buckets (compound after-tax).
      - Handle monthly coupons (e.g., external rent) with escalation; sweep to Liquid by default.
      - Pre-waterfall redemption of accrual-only buckets by user priority.
      - Horizon close-out: force MF redemption via FundingEngine; accrual-only buckets left invested unless redeemed.
    """

    def __init__(self, ctx: EngineContext, funding_engine):
        self.ctx = ctx
        self.funding = funding_engine
        pf: Portfolio = ctx.portfolio

        self.cfg: TreasuryConfig = getattr(pf, "treasury_config", TreasuryConfig()) or TreasuryConfig()
        self.buckets: List[TreasuryBucketModel] = getattr(pf, "treasury_buckets", []) or []

        # Runtime balances for accrual-only buckets (Liquid tracked in FundingEngine)
        self.state = TreasuryState()
        for b in self.buckets:
            if b.mode == "accrual_only" and b.id != "liquid":
                self.state.set(b.id, Decimal(str(b.opening_balance or 0)))

    # ------------- Helpers -------------
    @staticmethod
    def _monthly_rate_from_annual(pct_annual: Decimal) -> Decimal:
        # Effective monthly rate: (1+r)^(1/12)-1
        if pct_annual is None:
            return D(0)
        r = D(pct_annual) / D(100)
        return ( (D(1) + r) ** (D(1)/D(12)) ) - D(1)

    def _bucket_by_priority(self) -> List[TreasuryBucketModel]:
        return sorted([b for b in self.buckets if b.mode == "accrual_only" and b.id != "liquid"],
                      key=lambda x: int(getattr(x, "redeem_priority", 50)))

    # ------------- Month accruals -------------
    def accrue_month_end(self, posting: date, t: int, ledger: Ledger):
        """
        1) Idle cash accrual on Liquid (via FundingEngine.liquid_bal).
        2) Accrual-only buckets: compound balances.
        3) Coupons: post to liquid (if sweep), else reinvest.
        """
        # (1) Liquid idle return
        idle_pct = getattr(self.cfg, "idle_return_after_tax_pct", None)
        if idle_pct and self.funding.liquid_bal > 0:
            r_m = self._monthly_rate_from_annual(D(idle_pct))
            gain = self.funding.liquid_bal * r_m
            if gain > 0:
                gain = qrupee(gain)
                ledger.lines.append(LedgerLine(
                    date=posting, label="Treasury Idle Return (net)", category="income", amount=Money(gain),
                    reason="Monthly accrual on liquid as per treasury_config"
                ))
                self.funding.add_surplus(month_index=t, amount=gain)

        # (2) Accrual-only buckets (excluding Liquid)
        for b in self.buckets:
            if b.mode != "accrual_only" or b.id == "liquid":
                continue
            bal0 = self.state.get(b.id)
            if bal0 <= 0:
                continue
            r_m = self._monthly_rate_from_annual(D(b.post_tax_return_annual_pct or 0))
            gain = bal0 * r_m
            if gain > 0:
                new_bal = qrupee(bal0 + gain)
                self.state.set(b.id, new_bal)
                ledger.lines.append(LedgerLine(
                    date=posting, label=f"Treasury Accrual — {b.name}", category="income",
                    amount=Money(gain), reason="Accrual-only bucket monthly compounding"
                ))

        # (3) Coupons
        for b in self.buckets:
            if D(b.coupon_monthly or 0) <= 0:
                continue
            years = t // 12
            escal = (D(1) + (D(b.coupon_escalation_annual_pct or 0) / D(100))) ** D(years)
            cash = qrupee(D(b.coupon_monthly) * escal)
            if cash <= 0:
                continue
            ledger.lines.append(LedgerLine(
                date=posting, label=f"Treasury Coupon — {b.name}", category="income",
                amount=Money(cash), reason="Monthly coupon with yearly escalation"
            ))
            if b.coupon_sweep == "to_liquid":
                self.funding.add_surplus(month_index=t, amount=cash)
            else:
                # reinvest into the same bucket
                self.state.set(b.id, self.state.get(b.id) + cash)

    # ------------- Pre-waterfall redemption -------------
    def cover_shortfall(self, month_index: int, posting: date, shortfall: Decimal, ledger: Ledger) -> Decimal:
        """
        Redeem accrual-only buckets by priority before hitting the main FundingEngine waterfall.
        Returns the remaining need (>=0).
        """
        need = D(shortfall)
        if need <= 0:
            return D(0)

        for b in self._bucket_by_priority():
            if need <= 0:
                break
            bal = self.state.get(b.id)
            if bal <= 0:
                continue
            redeem = min(bal, need)
            if redeem > 0:
                self.state.set(b.id, bal - redeem)
                ledger.lines.append(LedgerLine(
                    date=posting, label=f"Treasury Redemption — {b.name}", category="funding_treasury",
                    amount=Money(redeem), reason="Pre-waterfall redemption by priority"
                ))
                self.funding.add_surplus(month_index=month_index, amount=redeem)
                need -= redeem
        return need

    # ------------- Horizon close-out -------------
    def horizon_closeout(self, posting: date, ledger: Ledger):
        """
        At horizon: force MF redemption (tax-tracked buckets) via existing FundingEngine,
        so short-horizon decisions reflect realized CGT. Accrual-only buckets remain invested.
        """
        try:
            if hasattr(self.funding, "redeem_all_mf"):
                self.funding.redeem_all_mf(posting=posting, ledger=ledger)
        except Exception:
            pass
