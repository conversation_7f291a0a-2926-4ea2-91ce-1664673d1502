from __future__ import annotations
from pydantic import BaseModel, Field
from typing import Dict
from decimal import Decimal

Percent = Decimal

class RentTDS194IB(BaseModel):
    monthly_threshold: Decimal = Decimal("50000")
    rate_pct: Percent = Decimal("5.0")

class IHP(BaseModel):
    self_occupied_interest_cap_per_person: Decimal = Decimal("200000")
    hp_loss_setoff_cap_per_person: Decimal = Decimal("200000")
    carry_forward_years: int = 8

class Section80C(BaseModel):
    cap_per_person: Decimal = Decimal("150000")

class MFTaxRule(BaseModel):
    holding_days_ltcg: int
    stcg_rate_pct: Percent
    ltcg_rate_pct: Percent
    ltcg_annual_exemption: Decimal = Decimal("0")
    indexation_allowed: bool = False

class MFTax(BaseModel):
    equity: MFTaxRule = MFTaxRule(
        holding_days_ltcg=365, stcg_rate_pct=Decimal("15.0"),
        ltcg_rate_pct=Decimal("10.0"), ltcg_annual_exemption=Decimal("100000"),
        indexation_allowed=False
    )
    debt: MFTaxRule = MFTaxRule(
        holding_days_ltcg=1095, stcg_rate_pct=Decimal("30.0"),
        ltcg_rate_pct=Decimal("20.0"), ltcg_annual_exemption=Decimal("0"),
        indexation_allowed=False
    )

class PropertyCGT(BaseModel):
    stcg_rate_pct: Percent = Decimal("30.0")
    ltcg_rate_pct: Percent = Decimal("20.0")
    holding_days_ltcg: int = 730

class TaxLaws(BaseModel):
    tds_rules: Dict[str, RentTDS194IB] = Field(default_factory=lambda: {"rent_194IB": RentTDS194IB()})
    ihp: IHP = IHP()
    section80c: Section80C = Section80C()
    mf_tax: MFTax = MFTax()
    property_cgt: PropertyCGT = PropertyCGT()
    cii: Dict[str, int] = Field(default_factory=dict)  # e.g., {"FY2024-25": 348}

def parse_tax_laws(content: dict) -> TaxLaws:
    if not isinstance(content, dict):
        content = {}
    return TaxLaws.parse_obj(content)
