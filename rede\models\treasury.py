from __future__ import annotations
from pydantic import BaseModel, Field
from typing import Literal, Optional
from decimal import Decimal
from ..types import Money, Percent, D

BucketMode = Literal["accrual_only", "tax_tracked"]
Liquidity = Literal["at_call", "T+1", "locked"]
CouponSweep = Literal["to_liquid", "reinvest"]

class TreasuryBucketModel(BaseModel):
    id: str
    name: str
    mode: BucketMode = "accrual_only"

    # Opening balance for accrual-only instruments (FD/bonds/LIC/assured deals)
    opening_balance: Money = D(0)

    # Annual after-tax return for accrual-only buckets (compounded monthly)
    post_tax_return_annual_pct: Percent = D(0)

    # Liquidity metadata (informational + for guardrails)
    liquidity: Liquidity = "at_call"
    lock_months: Optional[int] = None

    # Redemption priority within Treasury (lower number = earlier to redeem)
    redeem_priority: int = 50

    # Optional monthly coupon (e.g., rent or assured coupon). Can escalate yearly.
    coupon_monthly: Money = D(0)
    coupon_escalation_annual_pct: Percent = D(0)
    coupon_sweep: CouponSweep = "to_liquid"  # default: sweep to liquid

    # For tax-tracked buckets that we want to show in Treasury but not accrue monthly
    asset_type: Optional[Literal["equity", "debt"]] = None  # MF-like taxation if redeemed
    exit_load_pct: Percent = D(0)  # display only; MF redemption uses existing engine

class TreasuryConfig(BaseModel):
    # Idle cash monthly accrual (replaces old alt_invest_return_after_tax_pct semantics)
    idle_return_after_tax_pct: Percent = D(8)  # editable default
    accrue_on_reserved: bool = True            # if you later track reserved pots
    enable_advanced_base: bool = False         # hook for rent-vs-buy delta base

class TreasuryState(BaseModel):
    # Runtime balances for accrual-only buckets (excluding Liquid)
    balances: dict[str, Decimal] = Field(default_factory=dict)

    def get(self, bucket_id: str) -> Decimal:
        return self.balances.get(bucket_id, D(0))

    def set(self, bucket_id: str, val: Decimal):
        self.balances[bucket_id] = D(val)
