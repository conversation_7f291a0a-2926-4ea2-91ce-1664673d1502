from __future__ import annotations
from datetime import date
from decimal import Decimal
from typing import Dict
from ..types import Money, D
from ..engine.ledger import Ledger, LedgerLine
from ..models.property import Property
from ..engine.tax_engine import fy_label
from ..utils.money import pct_to_decimal

class ExitEngine:
    """
    Realized sale + MTM computations with robust acquisition basis resolution.
    """

    # ---------- Acquisition basis resolver ----------
    def resolve_acquisition_basis(self, prop: Property, tracked_uc_total: Decimal) -> Decimal:
        total = D(tracked_uc_total or 0)
        if total <= 0 and prop.existing_basis:
            eb = prop.existing_basis
            total = (D(eb.purchase_total) + D(eb.stamp_duty_paid) +
                     D(eb.registration_fee_paid) + D(eb.other_acquisition_costs) +
                     D(eb.improvements_capitalized))
        for extra_name in ("stamp_duty_paid", "registration_fee_paid", "legal_misc_purchase_costs"):
            v = D(getattr(prop.heads, extra_name, 0))
            if v > 0:
                total += v
        return total

    # ---------- Realized sale ----------
    def realized_sale(
        self,
        *,
        posting: date,
        t: int,
        prop: Property,
        current_value: Decimal,
        outstanding_principal: Decimal,
        acquisition_cost_total: Decimal,
        ledger: Ledger,
        tax_engine,
    ) -> Dict[str, float]:
        gross = current_value
        brokerage = gross * pct_to_decimal(prop.exit.brokerage_pct)
        transfer = D(prop.exit.transfer_fee_fixed or 0)
        net_before_tds = gross - brokerage - transfer

        buyer_tds = net_before_tds * pct_to_decimal(prop.exit.buyer_tds_pct)
        cash_in = net_before_tds - buyer_tds

        ledger.lines.append(LedgerLine(
            date=posting, label="Sale Proceeds (net of buyer TDS)", category="sale_in",
            amount=Money(cash_in), property_id=prop.id,
            reason="Gross - brokerage - transfer - buyer TDS (194-IA)"
        ))
        if brokerage > 0:
            ledger.lines.append(LedgerLine(
                date=posting, label="Brokerage on Sale", category="sale_cost",
                amount=Money(-brokerage), property_id=prop.id
            ))
        if transfer > 0:
            ledger.lines.append(LedgerLine(
                date=posting, label="Transfer Fee", category="sale_cost",
                amount=Money(-transfer), property_id=prop.id
            ))

        penalty = D(0)
        if outstanding_principal > 0:
            if getattr(prop.loan, "foreclosure_penalty_pct", None):
                penalty = outstanding_principal * pct_to_decimal(prop.loan.foreclosure_penalty_pct)
            ledger.lines.append(LedgerLine(
                date=posting, label="Loan Closure - Principal", category="loan_closure",
                amount=Money(-outstanding_principal), property_id=prop.id
            ))
            if penalty > 0:
                ledger.lines.append(LedgerLine(
                    date=posting, label="Loan Foreclosure Penalty", category="loan_closure",
                    amount=Money(-penalty), property_id=prop.id
                ))

        cgt = tax_engine.compute_property_cgt(
            prop=prop, sale_date=posting, sale_gross=gross,
            transaction_costs=brokerage + transfer, acquisition_total=acquisition_cost_total
        )
        if cgt > 0:
            if prop.exit.cgt_pay_timing == "immediate":
                ledger.lines.append(LedgerLine(
                    date=posting, label=f"Property CGT ({fy_label(posting)})", category="tax",
                    amount=Money(-cgt), property_id=prop.id
                ))
            else:
                tax_engine.defer_property_cgt(posting, cgt)

        net_sale_cash = cash_in - outstanding_principal - penalty - (cgt if prop.exit.cgt_pay_timing == "immediate" else D(0))
        return {
            "gross_sale": float(gross),
            "cash_from_buyer_net_tds": float(cash_in),
            "brokerage": float(brokerage),
            "transfer": float(transfer),
            "loan_closure_principal": float(outstanding_principal),
            "foreclosure_penalty": float(penalty),
            "cgt_paid_now": float(cgt if prop.exit.cgt_pay_timing == "immediate" else 0.0),
            "net_realized_cash_after_closure": float(net_sale_cash),
        }

    # ---------- MTM ----------
    def mtm_net_value(
        self,
        *,
        t: int,
        prop: Property,
        current_value: Decimal,
        outstanding_principal: Decimal,
        include_cgt: bool,
        tax_engine
    ) -> Dict[str, float]:
        brokerage = current_value * pct_to_decimal(prop.exit.brokerage_pct)
        transfer = D(prop.exit.transfer_fee_fixed or 0)
        buyer_tds = (current_value - brokerage - transfer) * pct_to_decimal(prop.exit.buyer_tds_pct)
        net_cash_if_sold = (current_value - brokerage - transfer) - buyer_tds

        cgt_est = D(0)
        if include_cgt:
            cgt_est = tax_engine.estimate_property_cgt_now(prop=prop, sale_gross=current_value,
                                                           transaction_costs=brokerage + transfer)

        net_after_loan = net_cash_if_sold - outstanding_principal - cgt_est
        return {
            "mtm_gross": float(current_value),
            "mtm_brokerage": float(brokerage),
            "mtm_transfer": float(transfer),
            "mtm_buyer_tds": float(buyer_tds),
            "mtm_cgt_included": float(cgt_est),
            "mtm_net_after_loan": float(net_after_loan),
        }
