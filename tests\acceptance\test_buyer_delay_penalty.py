import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger, PropertyOwnershipCosts, PropertyOccupancy
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_buyer_delay_penalty_postings_present_if_supported():
    """
    Goal: If user delays paying a demand, builder penalty (e.g. 18% pa)
    should be posted until paid.

    We simulate:
      - Demand at m=6
      - Liquid cash insufficient (forces delay)
    Expect:
      - If penalty logic exists, ledger shows penalty postings after m=6
    """
    ctx = _ctx()

    ev = PaymentEvent(
        name="Slab", stage="Slab",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=6),
        percent_of_base=DD(50), gst_pct=DD(0), financeable_by_bank=False
    )
    prop = Property(
        id="PEN1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(2_000_000)),
        plan=PaymentPlan(events=[ev]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    person = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(100_000))

    # Portfolio with insufficient liquidity
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
        persons=[person], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(100_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
    ledger = eng.result()["ledger"]

    penalties = [l for l in ledger if "penalty" in l.get("category","").lower() or "delay" in l.get("category","").lower()]
    if not penalties:
        pytest.skip("No penalty postings in ledger → penalty feature not yet wired")
    # If present, must appear after demand month
    assert all(l["month_index"] >= 6 for l in penalties)
