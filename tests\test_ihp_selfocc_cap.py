from datetime import date
from decimal import Decimal as D
from rede.engine.tax_engine import TaxEngine, fy_label
from rede.engine.context import EngineContext
from rede.engine.ledger import Ledger
from rede.models.core import Portfolio, Person
from rede.models.property import Property, PropertyHeads
from rede.types import D as DD

def test_section_24b_cap_self_occupied():
    # Build a simple portfolio with one person + one property (self-occupied)
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(0))
    prop = Property(id="P1", city="BLR", heads=PropertyHeads(base_price_ex_gst=DD(0)))
    pf = Portfolio(currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
                   persons=[p], properties=[prop], mf_holdings=[],
                   user_starting_liquid_cash_by_person={"u": DD(0)},
                   alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
                   funding_strategy=None, tax_settings_global=None, configs=None)
    class _Cfg: content = {"ihp": {"self_occupied_interest_cap_per_person": 200000, "hp_loss_setoff_cap_per_person": 200000, "carry_forward_years": 8}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()

    tax = TaxEngine(ctx, pf)
    ledger = Ledger()

    # Accrue post-OC interest across FY 2025-26 totaling 300,000
    for m in range(12):
        tax.accrue_interest(prop, date(2025, 4, 5).replace(month=4+m if 4+m<=12 else (4+m-12), year=2025 if 4+m<=12 else 2026),
                            D(25_000), is_preconstruction=False, is_letout=False)
    fy = fy_label(date(2025, 6, 1))
    tax.settle_fy(fy, ledger)
    panel = tax.get_panel()
    assert round(panel["ihp"]["selfocc_total_interest_allowed"], 2) == 200000.00
