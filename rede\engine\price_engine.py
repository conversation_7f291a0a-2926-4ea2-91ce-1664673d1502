from __future__ import annotations
from decimal import Decimal
from ..types import Money, Percent, D
from ..models.property import Property
from ..models.legal import OCStatus
from ..utils.money import pct_to_decimal

def _agreement_base_ex_gst(prop: Property) -> Decimal:
    h = prop.heads
    return D(h.base_price_ex_gst) + D(h.plc_floor_rise) + D(h.parking_charges) + D(h.club_membership) + D(h.other_capital_heads)

class PriceEngine:
    """
    v1.4 §4: CAGR + shocks (+ optional age & legal discounts).
    """

    def _shock_additive_monthly(self, prop: Property, t: int) -> Decimal:
        extra = Decimal("0")
        for s in prop.price_model.shocks:
            if s.start_month <= t <= s.end_month:
                months = max(1, s.end_month - s.start_month + 1)
                extra += pct_to_decimal(Percent(D(s.cumulative_pct))) / D(months)
        return extra

    def _age_discount(self, prop: Property, t: int) -> Decimal:
        if not prop.price_model.age_factor_curve:
            return Decimal("0")
        age_years = t // 12
        disc = Decimal("0")
        for pt in prop.price_model.age_factor_curve:
            if age_years >= pt.age_year:
                disc = max(disc, pct_to_decimal(Percent(D(pt.discount_pct))))
        return disc

    def _legal_discount(self, prop: Property) -> Decimal:
        lp = prop.legal
        disc = Decimal("0")
        if getattr(lp, "b_khata_discount_pct", None):
            disc = max(disc, pct_to_decimal(Percent(D(lp.b_khata_discount_pct))))
        if getattr(lp, "oc_status", None) is not None and lp.oc_status != OCStatus.GRANTED:
            if getattr(lp, "oc_risk_discount_pct", None):
                disc = max(disc, pct_to_decimal(Percent(D(lp.oc_risk_discount_pct))))
        return disc

    def compute_month_value(self, prop: Property, t: int, prev_value: Money | None) -> Money:
        A = Decimal("0")
        for head in prop.appreciation_base:
            A += D(getattr(prop.heads, head))

        g_annual = Percent(D(prop.price_model.base_annual_cagr_pct))
        g_month = pct_to_decimal(g_annual) / D(12)
        delta = self._shock_additive_monthly(prop, t)

        if prev_value is None or prev_value == 0:
            base0 = A
            v_nominal = base0 * (Decimal("1") + g_month + delta)
        else:
            v_nominal = Decimal(prev_value) * (Decimal("1") + g_month + delta)

        age_disc = self._age_discount(prop, t)
        legal_disc = self._legal_discount(prop)
        v_after = v_nominal * (Decimal("1") - age_disc) * (Decimal("1") - legal_disc)
        v_after += self._interiors_value_component(prop, t)
        return Money(v_after)


    def _interiors_value_component(self, prop: Property, t: int) -> Decimal:
        try:
            model = prop.price_model
            heads = prop.heads
            if not getattr(model, 'interiors_in_value', False):
                return Decimal('0')
            capex = Decimal(str(getattr(heads, 'interiors_capex', 0) or 0))
            if capex <= 0:
                return Decimal('0')
            yrs = int(getattr(model, 'interiors_depr_years', 10) or 10)
            residual_pct = Decimal(str(getattr(model, 'interiors_residual_pct', 20))) / Decimal('100')
            age_years = Decimal(t) / Decimal(12)
            depreciable = capex * (Decimal('1') - residual_pct)
            per_year = depreciable / Decimal(max(1, yrs))
            depreciated = capex - per_year * age_years
            return max(Decimal('0'), depreciated)
        except Exception:
            return Decimal('0')
