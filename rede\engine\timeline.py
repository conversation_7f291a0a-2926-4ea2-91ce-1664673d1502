from datetime import date as _d, date
from decimal import Decimal

from ..types import Money, D
from ..models.core import Portfolio
from ..models.property import Property
from ..engine.context import EngineContext
from ..engine.ledger import Ledger, LedgerLine
from .price_engine import PriceEngine
from .loan_engine import LoanEngine
from .funding_engine import FundingEngine
from .tax_engine import TaxEngine, fy_label
from ..utils.money import pct_to_decimal
from .exit_engine import ExitEngine
from ..utils.xirr import xirr
from .treasury import TreasuryEngine
from ..utils.units_guardrail import apply_units_guardrail


def _iso_to_date(iso: str) -> date:
    y, m, d = map(int, iso.split("-"))
    return date(y, m, d)


class TimelineEngine:
    def __init__(self, ctx: EngineContext):
        self.ctx = ctx
        self.ledger = Ledger()
        self.price = PriceEngine()
        self.loan = LoanEngine()
        self.funding = FundingEngine(ctx)
        self.tax = TaxEngine(ctx, ctx.portfolio)
        self.exit = ExitEngine()

        # Track cost basis for property CGT
        self.acq_total_by_prop: dict[str, Decimal] = {}
        self.first_acq_date_by_prop: dict[str, date] = {}

        # Narrative capture buffers
        self._events: list[dict] = []          # {month, date, property_id, type, note, amount}
        self._largest_builder: dict[str, Decimal] = {}

        # Risk flags
        self.risk_flags: list[str] = []

        # Rent TDS credits accumulator (also fed to tax engine)
        self.tds_credits = D(0)

        # NEW: track tenant security deposits for proper refund/transfer
        self._secdep: dict[str, Decimal] = {}

    def _log_event(self, t: int, posting: date, prop_id: str | None, etype: str, note: str, amount: Decimal | None = None):
        self._events.append({
            "month": t, "date": posting.isoformat(),
            "property_id": prop_id, "type": etype,
            "note": note, "amount": float(amount) if amount is not None else None
        })

    def run(self):
        # 0) OD interest (carry-in) → 1) Price path → 2) Builder/TDS → 3) Loan charges
        # 4) Ownership costs → 5) Occupancy (rent & TDS) → 6) Waterfall
        # 7) Month-end OD interest → 7b) Treasury accruals → 8) FY settlement → 8b) Horizon close-out
        pf: Portfolio = self.ctx.portfolio
        start = _iso_to_date(pf.start_date)

        # Units guardrail — repair 0.06→6% type inputs softly
        apply_units_guardrail(pf, self.risk_flags)

        # Unified Treasury (uses FundingEngine.liquid_bal; accrual-only bucket balances tracked here)
        self.treasury = TreasuryEngine(self.ctx, self.funding)

        bank_paid_so_far: dict[str, Decimal] = {p.id: D(0) for p in pf.properties}
        outstanding: dict[str, Decimal] = {p.id: D(0) for p in pf.properties}
        current_value: dict[str, Money] = {p.id: Money(D(0)) for p in pf.properties}

        possession_month: dict[str, int | None] = {p.id: None for p in pf.properties}
        oc_month: dict[str, int | None] = {p.id: None for p in pf.properties}
        subvention_until: dict[str, int | None] = {p.id: None for p in pf.properties}

        # Build event schedule
        schedule: dict[int, list[tuple[Property, object]]] = {}
        for prop in pf.properties:
            for ev in prop.plan.events:
                m = self._event_triggers_month(pf, ev)
                if m is None:
                    continue
                schedule.setdefault(m, []).append((prop, ev))

        # Rent TDS 194-IB config
        rent_tds_cfg = self.ctx.tax_laws_fy.content.get("tds_rules", {}).get("rent_194IB", {})
        rent_tds_threshold = Decimal(str(rent_tds_cfg.get("monthly_threshold", 50000)))
        rent_tds_rate = Decimal(str(rent_tds_cfg.get("rate_pct", 5))) / Decimal(100)

        last_settled_fy: str | None = None
        realized_sales_done: dict[str, bool] = {p.id: False for p in pf.properties}

        for t in range(pf.horizon_months + 1):
            posting = self._posting_date(start, t, pf.posting_day_of_month)
            self.funding.current_month_index = t  # used by horizon redemption helpers

            month_inflow = D(0)
            month_outflow = D(0)

            # 0) OD interest from previous month is due now
            od_interest_due = self.funding.begin_month(posting)
            if od_interest_due > 0:
                amt = Money(-od_interest_due)
                self.ledger.lines.append(LedgerLine(
                    date=posting, label="OD Interest (due)", category="funding_od",
                    amount=amt, reason="Previous month's OD interest payable"
                ))
                month_outflow += D(amt)

            # 1) Price path
            for prop in pf.properties:
                prev = current_value[prop.id]
                v_t = self.price.compute_month_value(prop, t, prev)
                current_value[prop.id] = v_t

            # 2) Builder events / TDS 194-IA split
            for prop, ev in schedule.get(t, []):
                base_B = self._agreement_base_ex_gst(prop)
                # Mark possession/OC for downstream gates
                if getattr(ev, "trigger_type", None) and getattr(ev.trigger_type, "value", None) in ("possession", "handover"):
                    possession_month[prop.id] = t
                if getattr(ev, "trigger_type", None) and getattr(ev.trigger_type, "value", None) == "oc_received":
                    oc_month[prop.id] = t
                    self.tax.set_preconstruction_start_on_oc(prop, posting)
                if getattr(ev, "subvention", None):
                    if ev.subvention.end_trigger == "possession":
                        subvention_until[prop.id] = possession_month[prop.id]
                    elif ev.subvention.end_trigger == "oc_received":
                        subvention_until[prop.id] = oc_month[prop.id]
                    elif ev.subvention.end_trigger == "month" and ev.subvention.end_month is not None:
                        subvention_until[prop.id] = ev.subvention.end_month

                base_amt, gst_amt = self._event_amount(prop, ev, base_B)
                financeable = base_amt if ev.financeable_by_bank else D(0)
                disbursal = self.loan.compute_disbursal(self.ctx, prop, ev.stage or ev.name, Money(financeable), Money(bank_paid_so_far[prop.id]))
                bank_paid_so_far[prop.id] += D(disbursal)
                outstanding[prop.id] += D(disbursal)

                # Purchase TDS 194-IA (1% default; heads.tds_buy_pct)
                tds_buy_pct = D(prop.heads.tds_buy_pct or 0) / D(100)
                tds_buy = base_amt * tds_buy_pct

                # Margin cash: (base - disbursal) + GST
                margin_cash_total = (base_amt - D(disbursal)) + gst_amt

                # Split margin into builder (net of TDS) + TDS to govt
                builder_pay = margin_cash_total - tds_buy
                if builder_pay < 0:
                    builder_pay = D(0)

                if builder_pay != 0:
                    self.ledger.lines.append(LedgerLine(
                        date=posting, label=f"Builder Payment (net of TDS): {ev.name}",
                        category="purchase", amount=Money(-builder_pay), property_id=prop.id,
                        reason="Base minus disbursal + GST - TDS194IA",
                        formula_hint="(base-disbursal)+gst - tds"
                    ))
                    month_outflow += D(Money(-builder_pay))
                    self._log_event(t, posting, prop.id, "builder_payment",
                                    f"Builder demand '{ev.name}' paid (net of TDS)", D(builder_pay))
                    prev_big = self._largest_builder.get(prop.id, D(0))
                    if D(builder_pay) > prev_big:
                        self._largest_builder[prop.id] = D(builder_pay)

                if tds_buy > 0:
                    self.ledger.lines.append(LedgerLine(
                        date=posting, label="Purchase TDS 194-IA", category="tax",
                        amount=Money(-tds_buy), property_id=prop.id,
                        reason="1% TDS on consideration (ex-GST) paid to Govt",
                        formula_hint="tds=base*1%"
                    ))
                    month_outflow += D(Money(-tds_buy))
                    self._log_event(t, posting, prop.id, "purchase_tds",
                                    "Purchase TDS 194-IA remitted", D(tds_buy))

            # 3) Loan EMI / pre-EMI (+ shocks); tax accruals for interest/principal
            for prop in pf.properties:
                if prop.loan and outstanding[prop.id] > 0:
                    self.loan.maybe_apply_rate_shock(prop.loan, t, posting, Money(outstanding[prop.id]))
                    roi_m = self.loan.monthly_rate(self.ctx, prop.loan, posting, t)
                    emi_started = (prop.loan.emi_start_month is not None) and (t >= prop.loan.emi_start_month)
                    active_subvention = (subvention_until[prop.id] is not None and t <= subvention_until[prop.id])

                    ocm = oc_month[prop.id]
                    letoutm = prop.occupancy.let_out_month
                    is_letout = (letoutm is not None and ocm is not None and t >= max(letoutm, ocm))
                    is_precon = (ocm is None) or (t < ocm)

                    if emi_started:
                        if not hasattr(prop.loan, "current_emi") or prop.loan.current_emi is None:
                            princ0 = Money(outstanding[prop.id])
                            princ0 = self.loan.apply_mrta_upfront(prop.loan, princ0)
                            outstanding[prop.id] = D(princ0)
                            prop.loan.current_emi = self.loan.compute_emi(Money(outstanding[prop.id]), roi_m, prop.loan.tenure_months)

                        interest = D(outstanding[prop.id]) * roi_m
                        principal_pay = D(prop.loan.current_emi) - interest
                        if principal_pay < 0:
                            principal_pay = D(0)
                        outstanding[prop.id] = max(D(0), D(outstanding[prop.id]) - principal_pay)

                        # --- NEW: handle EMI recompute after top-up (policy: increase EMI, keep tenure) ---
                        policy = getattr(prop.loan, "_recalc_emi_from_next_month", None)
                        if policy == "increase_emi" and prop.loan.emi_start_month is not None and t >= prop.loan.emi_start_month:
                            remaining = max(1, prop.loan.tenure_months - (t - prop.loan.emi_start_month))
                            roi_m2 = self.loan.monthly_rate(self.ctx, prop.loan, posting, t)
                            prop.loan.current_emi = self.loan.compute_emi(Money(outstanding[prop.id]), roi_m2, remaining)
                            setattr(prop.loan, "_recalc_emi_from_next_month", None)
                        # --- END NEW ---


                        # Tax accruals:
                        self.tax.accrue_interest(prop, posting, interest, is_preconstruction=is_precon, is_letout=is_letout)
                        if not is_precon and principal_pay > 0:
                            self.tax.accrue_principal_80c(prop, posting, principal_pay)

                        if not (active_subvention and getattr(prop.plan.events[0].subvention, "coverage", "") == "emi"):
                            self.ledger.lines.append(LedgerLine(
                                date=posting, label="EMI", category="emi", amount=Money(-prop.loan.current_emi),
                                property_id=prop.id, reason="Monthly EMI",
                                formula_hint="EMI=P*i*(1+i)^n/((1+i)^n-1)"
                            ))
                            month_outflow += D(Money(-prop.loan.current_emi))

                            # Optional loan prepayment(s) this month
                            for _pp in (getattr(prop.loan, 'prepayments', []) or []):
                                try:
                                    _pp_month = getattr(_pp, 'month', None) if not isinstance(_pp, dict) else _pp.get('month')
                                    if _pp_month != t:
                                        continue
                                    _pp_amt = D(getattr(_pp, 'amount', 0) if not isinstance(_pp, dict) else _pp.get('amount', 0))
                                    _pp_mode = (getattr(_pp, 'mode', 'reduce_tenure') if not isinstance(_pp, dict) else _pp.get('mode', 'reduce_tenure'))
                                    if _pp_amt > 0:
                                        pay = min(_pp_amt, D(outstanding[prop.id]))
                                        if pay > 0:
                                            self.ledger.lines.append(LedgerLine(
                                                date=posting, label='Loan Prepayment', category='loan_prepay', amount=Money(-pay),
                                                property_id=prop.id, reason=_pp_mode
                                            ))
                                            month_outflow += D(Money(-pay))
                                            outstanding[prop.id] = max(D(0), D(outstanding[prop.id]) - pay)
                                            if _pp_mode == 'reduce_emi' and prop.loan.emi_start_month is not None and t >= prop.loan.emi_start_month:
                                                rem = max(1, prop.loan.tenure_months - (t - prop.loan.emi_start_month))
                                                roi_m2 = self.loan.monthly_rate(self.ctx, prop.loan, posting, t)
                                                prop.loan.current_emi = self.loan.compute_emi(Money(outstanding[prop.id]), roi_m2, rem)
                                except Exception:
                                    pass
                    else:
                        preemi = D(outstanding[prop.id]) * roi_m
                        # Tax: pre-EMI interest is pre-construction
                        self.tax.accrue_interest(prop, posting, preemi, is_preconstruction=True, is_letout=False)

                        if not (active_subvention and getattr(prop.plan.events[0].subvention, "coverage", "") == "pre_emi"):
                            self.ledger.lines.append(LedgerLine(
                                date=posting, label="Pre-EMI (interest only)", category="preemi",
                                amount=Money(-preemi), property_id=prop.id,
                                reason="Interest on disbursed principal", formula_hint="preEMI=principal*i_m"
                            ))
                            month_outflow += D(Money(-preemi))

            # 4) Ownership costs
            for prop in pf.properties:
                # Maintenance after possession/handover
                if possession_month[prop.id] is not None and t >= possession_month[prop.id]:
                    oc = prop.ownership_costs
                    years_since_poss = (t - possession_month[prop.id]) // 12
                    m_rate_psf = D(oc.monthly_maintenance_per_sqft) * ((D(1) + pct_to_decimal(oc.annual_maintenance_escalation_pct)) ** D(years_since_poss))
                    maintenance = m_rate_psf * Decimal(oc.area_sqft)
                    if maintenance > 0:
                        amt = Money(-maintenance)
                        self.ledger.lines.append(LedgerLine(
                            date=posting, label="Maintenance", category="maintenance", amount=amt,
                            property_id=prop.id, reason="Rate_psf*area with annual escalation",
                            formula_hint="m = rate_psf*(1+esc)^years * area"
                        ))
                        month_outflow += D(amt)

                # Property tax yearly (on start-month anniversary)
                if prop.ownership_costs.annual_property_tax and posting.month == start.month and posting.day == min(pf.posting_day_of_month, 28):
                    tax_amt = D(prop.ownership_costs.annual_property_tax)
                    if tax_amt > 0:
                        amt = Money(-tax_amt)
                        self.ledger.lines.append(LedgerLine(
                            date=posting, label="Property Tax (annual)", category="maintenance", amount=amt,
                            property_id=prop.id, reason="Annual municipal tax", formula_hint="annual fixed"
                        ))
                        month_outflow += D(amt)
                        self.tax.accrue_property_tax(prop, posting, tax_amt)

                # Insurance yearly with escalation
                if prop.ownership_costs.annual_insurance and posting.month == start.month and posting.day == min(pf.posting_day_of_month, 28):
                    oc = prop.ownership_costs
                    years = (t // 12)
                    ins = D(oc.annual_insurance) * ((D(1) + pct_to_decimal(oc.insurance_escalation_pct)) ** D(years))
                    if ins > 0:
                        amt = Money(-ins)
                        self.ledger.lines.append(LedgerLine(
                            date=posting, label="Home Insurance (annual)", category="maintenance", amount=amt,
                            property_id=prop.id, reason="Annual with escalation", formula_hint="ins0*(1+esc)^years"
                        ))
                        month_outflow += D(amt)

                # Leasehold ground rent yearly
                if prop.legal.annual_ground_rent and posting.month == start.month and posting.day == min(pf.posting_day_of_month, 28):
                    gr = D(prop.legal.annual_ground_rent)
                    if gr > 0:
                        amt = Money(-gr)
                        self.ledger.lines.append(LedgerLine(
                            date=posting, label="Ground Rent (annual)", category="maintenance", amount=amt,
                            property_id=prop.id, reason="Leasehold ground rent", formula_hint="annual fixed"
                        ))
                        month_outflow += D(amt)

            # 5) Occupancy flows (rent paid, saved, received)
            for prop in pf.properties:
                # Rent paid while waiting (if renting elsewhere)
                if prop.occupancy.continue_renting_elsewhere:
                    move_in = prop.occupancy.move_in_month
                    if (move_in is None or t < move_in):
                        years = t // 12
                        base_rent = D(prop.occupancy.current_rent_per_month)
                        rent = base_rent * ((D(1) + pct_to_decimal(prop.occupancy.annual_rent_escalation_pct)) ** D(years))
                        if rent > 0:
                            amt = Money(-rent)
                            self.ledger.lines.append(LedgerLine(
                                date=posting, label="Rent Paid (elsewhere)", category="rent_paid", amount=amt,
                                property_id=prop.id, reason="Pre-OC/possession housing", formula_hint="rent0*(1+esc)^years"
                            ))
                            month_outflow += D(amt)
                            self._log_event(t, posting, prop.id, "rent_paid", "Rent paid while awaiting possession/OC", rent)

                # Rent saved after move-in (OC gate)
                if prop.occupancy.continue_renting_elsewhere:
                    move_in = prop.occupancy.move_in_month
                    if move_in is not None and t >= move_in and oc_month[prop.id] is not None and t >= oc_month[prop.id]:
                        years = t // 12
                        base_rent = D(prop.occupancy.current_rent_per_month)
                        saved = base_rent * ((D(1) + pct_to_decimal(prop.occupancy.annual_rent_escalation_pct)) ** D(years))
                        if saved > 0:
                            amt = Money(saved)
                            self.ledger.lines.append(LedgerLine(
                                date=posting, label="Rent Saved", category="rent_saved", amount=amt,
                                property_id=prop.id, reason="After move-in", formula_hint="rent0*(1+esc)^years"
                            ))
                            month_inflow += D(amt)
                            self._log_event(t, posting, prop.id, "rent_saved", "Rent saved after move-in (OC gate passed)", saved)

                # Rent received if let-out (OC gate)
                let_out = prop.occupancy.let_out_month
                if let_out is not None and t >= let_out and oc_month[prop.id] is not None and t >= oc_month[prop.id]:
                    # Gross monthly rent (yield * value / 12) OR escalated fixed rent
                    if prop.occupancy.link_rent_yield_to_value:
                        yield_m = pct_to_decimal(prop.occupancy.rent_yield_pct_of_value) / D(12)
                        gross = D(current_value[prop.id]) * yield_m
                    else:
                        years_since_let = max(0, (t - let_out) // 12)
                        base = D(prop.occupancy.rent_per_month or 0)
                        rent_esc = pct_to_decimal(prop.occupancy.annual_rent_escalation_pct)
                        gross = base * ((D(1) + rent_esc) ** D(years_since_let))

                    # Vacancy/collection
                    vac = pct_to_decimal(prop.occupancy.vacancy_pct)
                    coll = pct_to_decimal(prop.occupancy.collection_efficiency_pct)
                    effective = gross * (D(1) - vac) * coll

                    # First month of tenancy: brokerage and deposit
                    if t == let_out:
                        brokerage = prop.occupancy.letting_brokerage_months * float(gross)
                        if brokerage > 0:
                            amt = Money(-Decimal(brokerage))
                            self.ledger.lines.append(LedgerLine(
                                date=posting, label="Letting Brokerage", category="maintenance", amount=amt,
                                property_id=prop.id, reason="Brokerage on tenancy start",
                                formula_hint="months * gross_rent"
                            ))
                            month_outflow += D(amt)

                        if prop.occupancy.security_deposit_months and prop.occupancy.security_deposit_months > 0:
                            dep = Decimal(prop.occupancy.security_deposit_months) * gross
                            amt = Money(dep)
                            self.ledger.lines.append(LedgerLine(
                                date=posting, label="Security Deposit (in)", category="rent_received", amount=amt,
                                property_id=prop.id, reason="Deposit from tenant", formula_hint="months * gross_rent"
                            ))
                            month_inflow += D(amt)
                            # track for refund later
                            self._secdep[prop.id] = self._secdep.get(prop.id, D(0)) + dep

                    # PM fee (fixed / % / both)
                    pm_mode = getattr(prop.occupancy, "pm_fee_mode", "fixed")
                    fixed_fee = D(prop.occupancy.pm_fee_monthly or 0)
                    pct_fee = effective * pct_to_decimal(prop.occupancy.property_manager_fee_pct_of_rent)

                    if pm_mode == "percent":
                        pm_fee = pct_fee
                    elif pm_mode == "fixed":
                        pm_fee = fixed_fee
                    else:  # "both"
                        pm_fee = fixed_fee + pct_fee

                    if pm_fee > 0:
                        self.ledger.lines.append(LedgerLine(
                            date=posting, label="PM Fee", category="maintenance", amount=Money(-pm_fee),
                            property_id=prop.id,
                            reason=("PM fixed + % of effective rent" if pm_mode == "both"
                                    else "Property manager percentage on rent" if pm_mode == "percent"
                                    else "Property manager fixed monthly"),
                            formula_hint=("fixed + pct*effective_rent" if pm_mode == "both"
                                        else "pm_pct * effective_rent" if pm_mode == "percent"
                                        else "pm_fixed")
                        ))
                        month_outflow += D(Money(-pm_fee))

                    # Rent TDS 194-IB handling + IHP accruals
                    tds = D(0)
                    cash_in = effective
                    if gross >= rent_tds_threshold:
                        tds = gross * rent_tds_rate
                        cash_in = effective - tds
                        self.tds_credits += tds  # credit (not cash)
                    # Accrue IHP on gross effective (pre-TDS); record TDS credit separately
                    self.tax.accrue_rent_effective(prop, posting, cash_in + tds)
                    if tds > 0:
                        self.tax.accrue_rent_tds_credit(posting, tds)

                    if cash_in > 0:
                        amt = Money(cash_in)
                        self.ledger.lines.append(LedgerLine(
                            date=posting, label="Rent Received (net of TDS if applicable)", category="rent_received", amount=amt,
                            property_id=prop.id, reason="Yield-linked/fixed rent with vacancy/collection",
                            formula_hint="gross*(1-vac)*coll minus TDS if threshold"
                        ))
                        month_inflow += D(amt)
                        self._log_event(t, posting, prop.id, "rent_received", "Rent received (effective)", effective)

                    # NEW: Refund deposit at tenancy end if occupancy.tenancy_months is provided
                    tenancy_months = getattr(prop.occupancy, "tenancy_months", None)
                    if tenancy_months is not None:
                        end_m = let_out + int(tenancy_months)
                        if t == end_m and self._secdep.get(prop.id, D(0)) > 0:
                            refund = self._secdep[prop.id]
                            self.ledger.lines.append(LedgerLine(
                                date=posting, label="Security Deposit (out)", category="rent_received",
                                amount=Money(-refund), property_id=prop.id, reason="Deposit refunded to tenant"
                            ))
                            month_outflow += D(Money(-refund))
                            self._secdep[prop.id] = D(0)

            # 6) Waterfall
            net_month = month_inflow + month_outflow
            if net_month < 0:
                # Pre-waterfall: redeem accrual-only treasury buckets by user priority
                remaining = self.treasury.cover_shortfall(
                    month_index=t, posting=posting, shortfall=-net_month, ledger=self.ledger
                )
                if remaining > 0:
                    self.funding.cover_shortfall(
                        month_index=t,
                        posting=posting,
                        shortfall=remaining,
                        ledger=self.ledger,
                        bank_paid_so_far=bank_paid_so_far,
                        outstanding=outstanding,
                        properties=pf.properties,
                    )
                # HL Top-up risk flag if rung used this month (detect ledger line)
                if any(l.label == "HL Top-up" and l.date == posting for l in self.ledger.lines):
                    self.risk_flags.append(
                        f"Relied on HL Top-up of ₹{sum(float(l.amount) for l in self.ledger.lines if l.label=='HL Top-up' and l.date==posting):,.0f} in month {t} — bank approval not guaranteed."
                    )
            else:
                self.funding.add_surplus(month_index=t, amount=net_month)

            # 7) Accrue OD interest ONCE at month end
            self.funding.end_month_accrue_od_interest()

            # 7b) Treasury month-end: idle cash accrual + accrual-only buckets + coupons
            self.treasury.accrue_month_end(posting=posting, t=t, ledger=self.ledger)

            # 8) FY settlement boundary (March) — defer if horizon is this month
            current_fy = fy_label(posting)
            if current_fy != last_settled_fy and posting.month == 3:
                # Defer settlement if horizon happens now; we'll settle after horizon below.
                pass

            # 8b) Horizon close-out (force MF redemption; always settle CGT now)
            if t == pf.horizon_months:
                # If tenant exists and sale is configured this month, ensure deposit has been transferred/refunded before sale (handled below)
                # Force redemption of MF holdings so short-horizon decisions reflect realized CGT
                self.treasury.horizon_closeout(posting=posting, ledger=self.ledger)
                # ALWAYS settle CGT now so horizon reflects post-tax (including March horizons)
                self.tax.set_mf_redemptions(self.funding.mf_redemptions_log)
                current_fy = fy_label(posting)
                self.tax.settle_fy(current_fy, self.ledger)
                last_settled_fy = current_fy
            elif posting.month == 3 and current_fy != last_settled_fy:
                # Normal March settle when it isn't the horizon month
                self.tax.set_mf_redemptions(self.funding.mf_redemptions_log)
                self.tax.settle_fy(current_fy, self.ledger)
                last_settled_fy = current_fy

            # Acquisition basis tracking for any builder events in month t
            for prop, ev in schedule.get(t, []):
                base_B = self._agreement_base_ex_gst(prop)
                base_amt, gst_amt = self._event_amount(prop, ev, base_B)
                if base_amt + gst_amt > 0:
                    self.acq_total_by_prop[prop.id] = self.acq_total_by_prop.get(prop.id, D(0)) + (base_amt + gst_amt)
                    if prop.id not in self.first_acq_date_by_prop:
                        self.first_acq_date_by_prop[prop.id] = posting
                    # One-time buy-side brokerage on first acquisition month
                    try:
                        b_pct = (D(prop.heads.brokerage_buy_pct) if prop.heads.brokerage_buy_pct is not None else D(0)) / D(100)
                    except Exception:
                        b_pct = D(0)
                    if b_pct > 0:
                        brokerage = base_B * b_pct
                        if brokerage > 0:
                            amt = Money(-brokerage)
                            self.ledger.lines.append(LedgerLine(
                                date=posting, label='Brokerage (buy)', category='acquisition', amount=amt,
                                property_id=prop.id, reason='Percent of agreement base ex-GST',
                                formula_hint='agreement_base*brokerage_pct'
                            ))
                            month_outflow += D(amt)
                    setattr(prop, "_acq_total_cached", self.acq_total_by_prop[prop.id])

            # Execute REALIZED sale if configured
            for prop in pf.properties:
                if realized_sales_done[prop.id]:
                    continue
                if prop.exit.sale_month is not None and t == prop.exit.sale_month:
                    # If tenant active, transfer/refund held deposit before sale proceeds are computed
                    if self._secdep.get(prop.id, D(0)) > 0:
                        dep = self._secdep[prop.id]
                        self.ledger.lines.append(LedgerLine(
                            date=posting, label="Security Deposit (out – on sale)", category="rent_received",
                            amount=Money(-dep), property_id=prop.id, reason="Transferred/refunded on sale"
                        ))
                        month_outflow += D(Money(-dep))
                        self._secdep[prop.id] = D(0)

                    tracked_total = self.acq_total_by_prop.get(prop.id, D(0))
                    acq_total = self.exit.resolve_acquisition_basis(prop, tracked_total)
                    acq_total += D(getattr(prop.heads, "registration_fee_paid", 0))
                    acq_total += D(getattr(prop.heads, "legal_misc_purchase_costs", 0))
                    current_val = D(current_value[prop.id])
                    out_prin = outstanding[prop.id]

                    sale_summary = self.exit.realized_sale(
                        posting=posting, t=t, prop=prop, current_value=current_val,
                        outstanding_principal=out_prin, acquisition_cost_total=acq_total,
                        ledger=self.ledger, tax_engine=self.tax
                    )
                    outstanding[prop.id] = D(0)
                    realized_sales_done[prop.id] = True
                    self._log_event(t, posting, prop.id, "sale", "Property sold / exit executed", current_val)

        # ===== KPIs & XIRR =====
        # Realized XIRR: actual ledger cash only
        flows = []
        for line in self.ledger.lines:
            amount = D(line.amount)
            flows.append((_d.fromisoformat(str(line.date)), amount))
        realized_rate = None
        try:
            realized_rate = xirr(flows)
        except Exception:
            realized_rate = None
        self._realized_xirr = float(realized_rate) if realized_rate is not None else None

        # MTM XIRR: assume would-sell-now inflow at horizon for unsold
        mtm_flows = list(flows)
        last_posting = self._posting_date(start, pf.horizon_months, pf.posting_day_of_month)
        for prop in pf.properties:
            if not realized_sales_done[prop.id]:
                current_val = D(current_value[prop.id])
                out_prin = outstanding[prop.id]
                mtm = self.exit.mtm_net_value(
                    t=pf.horizon_months, prop=prop, current_value=current_val,
                    outstanding_principal=out_prin,
                    include_cgt=prop.exit.include_cgt_in_mtm, tax_engine=self.tax
                )
                mtm_flows.append((last_posting, D(mtm["mtm_net_after_loan"])))

        mtm_rate = None
        try:
            mtm_rate = xirr(mtm_flows)
        except Exception:
            mtm_rate = None
        self._mtm_xirr = float(mtm_rate) if mtm_rate is not None else None

        # ===== Narratives & Risk Flags =====
        narratives: list[str] = []

        # Possession / OC milestones
        for prop in pf.properties:
            if possession_month[prop.id] is not None:
                narratives.append(f"M{possession_month[prop.id]}: Possession milestone recorded for property {prop.id}.")
            if oc_month[prop.id] is not None:
                narratives.append(f"M{oc_month[prop.id]}: OC received; move-in/let-out permitted thereafter.")

        # Biggest builder demand per property
        for prop in pf.properties:
            big = self._largest_builder.get(prop.id)
            if big and big > 0:
                narratives.append(f"Highest single builder demand for {prop.id}: ₹{float(big):,.0f}.")

        # Peak OD & Min liquid
        if self.funding.peak_od > 0:
            narratives.append(f"Peak OD utilisation ₹{float(self.funding.peak_od):,.0f} in M{self.funding.peak_od_month}.")
        narratives.append(f"Minimum liquid balance ₹{float(self.funding.min_liquid):,.0f} in M{self.funding.min_liquid_month}.")

        # MF redemptions total (requires small hook in FundingEngine; see below)
        mf_total = getattr(self.ctx, "_mf_usage_total", D(0))
        if mf_total > 0:
            narratives.append(f"Total MF redemptions used for funding: ₹{float(mf_total):,.0f}.")

        # Rate shocks
        for prop in pf.properties:
            if prop.loan and prop.loan.rate_shocks:
                for rs in prop.loan.rate_shocks:
                    narratives.append(f"M{rs.month}: Loan rate shock applied on {prop.id} to {float(rs.new_index_pct):.2f}% index.")

        # Occupancy starts
        for prop in pf.properties:
            if prop.occupancy.let_out_month is not None:
                narratives.append(f"M{prop.occupancy.let_out_month}: Property {prop.id} let out; rent cashflows begin (OC gate enforced).")
            if prop.occupancy.move_in_month is not None:
                narratives.append(f"M{prop.occupancy.move_in_month}: Move-in planned for {prop.id} (post-OC).")

        # Risk flags:
        # 1) OD > 80% of limit
        if pf.funding_strategy.od_limit:
            if self.funding.peak_od >= D(pf.funding_strategy.od_limit) * D("0.80"):
                self.risk_flags.append("OD utilisation reached ≥80% of limit — liquidity stress risk.")

        # 2) MF reliance > 50% of initial liquid (heuristic)
        initial_liquid = sum((D(v) for v in (pf.user_starting_liquid_cash_by_person or {}).values()), D(0))
        if initial_liquid > 0 and mf_total > initial_liquid * D("0.50"):
            self.risk_flags.append("More than half of starting liquid was funded via MF redemptions — market risk elevated.")

        # 3) OC gate miss — if let_out/move_in earlier than OC month
        for prop in pf.properties:
            if prop.occupancy.let_out_month is not None and (oc_month[prop.id] is None or prop.occupancy.let_out_month < oc_month[prop.id]):
                self.risk_flags.append(f"Let-out month (M{prop.occupancy.let_out_month}) precedes OC — legal/operational risk.")
            if prop.occupancy.move_in_month is not None and (oc_month[prop.id] is None or prop.occupancy.move_in_month < oc_month[prop.id]):
                self.risk_flags.append(f"Move-in month (M{prop.occupancy.move_in_month}) precedes OC — legal/operational risk.")

        # 4) Sanction-risk advisory (if stress % > 0)
        if float(pf.loan_sanction_risk_pct) > 0:
            narratives.append(f"Sanction-risk stress: consider a scenario where final sanction is {float(pf.loan_sanction_risk_pct):.0f}% lower than expected to test liquidity.")

        # Store for result()
        self._narratives = narratives
        self._key_events = self._events

    # ===== Helper methods (required) =====

    def _posting_date(self, start: date, month_index: int, dom: int) -> date:
        """Return the posting date for month t keeping the chosen day-of-month."""
        y = start.year + (start.month - 1 + month_index) // 12
        m = (start.month - 1 + month_index) % 12 + 1
        d = min(dom, 28)  # keep it safe
        return date(y, m, d)

    def _event_triggers_month(self, pf: Portfolio, ev) -> int | None:
        """
        Resolve when a builder event triggers. We support common shapes:
          - ev.month (absolute)
          - ev.offset_months_from_start
          - ev.trigger_type in {"possession","handover","oc_received"} handled elsewhere for side effects.
        """
        m = getattr(ev, "month", None)
        if isinstance(m, int):
            return m
        off = getattr(ev, "offset_months_from_start", None)
        if isinstance(off, int):
            return max(0, off)
        return None

    def _agreement_base_ex_gst(self, prop) -> Decimal:
        """Base agreement value (ex-GST) used for stage % calculations. Safe fallback to 0."""
        try:
            base = getattr(prop.heads, "agreement_base_ex_gst", None)
            return D(base or 0)
        except Exception:
            return D(0)

    def _event_amount(self, prop, ev, agreement_base_ex_gst: Decimal) -> tuple[Decimal, Decimal]:
        """
        Compute (base_amt, gst_amt) for a builder event.
        Supported shapes:
          - ev.percent_of_agreement_base_ex_gst (0–100)
          - ev.base_amount_ex_gst and optional ev.gst_pct
          - ev.base_amount_including_gst and optional ev.gst_pct (we back out GST)
        Returns (0,0) if fields are missing.
        """
        # 1) % of agreement base
        pct = getattr(ev, "percent_of_agreement_base_ex_gst", None)
        if pct is not None:
            try:
                frac = D(pct) / D(100)
                base_amt = (agreement_base_ex_gst * frac)
                gst_pct = D(getattr(ev, "gst_pct", 0)) / D(100)
                gst_amt = base_amt * gst_pct
                return base_amt, gst_amt
            except Exception:
                pass

        # 2) Explicit ex-GST amount (+ optional gst_pct)
        base_ex = getattr(ev, "base_amount_ex_gst", None)
        if base_ex is not None:
            try:
                base_amt = D(base_ex)
                gst_pct = D(getattr(ev, "gst_pct", 0)) / D(100)
                gst_amt = base_amt * gst_pct
                return base_amt, gst_amt
            except Exception:
                pass

        # 3) Explicit incl-GST (+ optional gst_pct); back-out GST if pct present
        inc = getattr(ev, "base_amount_including_gst", None)
        if inc is not None:
            try:
                inc_amt = D(inc)
                gst_pct = D(getattr(ev, "gst_pct", 0))
                if gst_pct > 0:
                    r = gst_pct / D(100)
                    base_amt = inc_amt / (D(1) + r)
                    gst_amt = inc_amt - base_amt
                else:
                    base_amt = inc_amt
                    gst_amt = D(0)
                return base_amt, gst_amt
            except Exception:
                pass

        return D(0), D(0)

    def result(self):
        total_out = sum((l.amount for l in self.ledger.lines if l.amount < 0), D(0))
        total_in = sum((l.amount for l in self.ledger.lines if l.amount > 0), D(0))
        return {
            "kpis": {
                "total_outflows": float(-total_out),
                "total_inflows": float(total_in),
                "net_cash": float(total_in + total_out),
                "liquid_closing": float(self.funding.liquid_bal),
                "od_closing": float(self.funding.od_bal),
                "tds_credits_accumulated": float(self.tds_credits),
                "min_liquid_balance": float(self.funding.min_liquid),
                "min_liquid_month": int(self.funding.min_liquid_month),
                "peak_od_used": float(self.funding.peak_od),
                "peak_od_month": int(self.funding.peak_od_month),
                "realized_xirr": self._realized_xirr,
                "mtm_xirr": self._mtm_xirr,
            },
            "tax_panel_last_fy": self.tax.get_panel(),
            "risk_flags": self.risk_flags,
            "narrative_summary": self._narratives,
            "key_events": self._key_events,
            "ledger": [l.dict() for l in self.ledger.lines],
        }
