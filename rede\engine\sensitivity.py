from __future__ import annotations
from typing import Any, Dict, List, Callable, Optional
import copy
from ..utils.jsonpath_patch import apply_patches
from ..api.contracts import SensitivityRunnerSpec, OneWaySensitivity, ScenarioGridItem, SensitivityPatch

Metrics = Dict[str, Any]
CalculateFn = Callable[[Dict[str, Any]], Metrics]

class SensitivityRunner:
    """
    Applies JSONPath patches and runs scenarios.
    If calculate_callback is provided, we execute and collect KPIs.
    Otherwise we return the patched payloads for the caller to run.
    """

    def _apply_one_way(self, base: Dict[str, Any], spec: OneWaySensitivity) -> List[Dict[str, Any]]:
        out: List[Dict[str, Any]] = []
        for d in spec.deltas:
            clone = copy.deepcopy(base)
            path = spec.path
            if spec.delta_mode == "percent":
                # multiply existing numeric by (1 + d)
                # fetch existing value (best effort)
                cur = eval_path(clone, path)
                if isinstance(cur, (int, float)):
                    newv = cur * (1.0 + float(d))
                else:
                    newv = d  # fallback: set raw
            else:
                newv = d
            apply_patches(clone, [{"path": path, "value": newv}])
            out.append(clone)
        return out

    def run(
        self,
        base_portfolio: Dict[str, Any],
        spec: SensitivityRunnerSpec,
        *,
        calculate_callback: Optional[CalculateFn] = None
    ) -> Dict[str, Any]:
        results: Dict[str, Any] = {"base": None, "one_way": [], "grid": [], "multi": []}

        # Base
        if calculate_callback:
            results["base"] = calculate_callback(copy.deepcopy(base_portfolio))
        else:
            results["base"] = copy.deepcopy(base_portfolio)

        # One-way sensitivities
        for s in spec.one_way_sensitivities or []:
            variants = self._apply_one_way(base_portfolio, s)
            row = []
            for v in variants:
                row.append(calculate_callback(v) if calculate_callback else v)
            results["one_way"].append({"label": s.label, "items": row})

        # Scenario grid (named patches)
        if spec.scenario_grid:
            for g in spec.scenario_grid:
                assert isinstance(g, ScenarioGridItem)
                clone = copy.deepcopy(base_portfolio)
                apply_patches(clone, [p.dict() if hasattr(p, "dict") else p for p in g.patches])
                results["grid"].append({
                    "name": g.name,
                    "result": calculate_callback(clone) if calculate_callback else clone
                })

        # Multi-factor orthogonal levels
        if spec.multi_factor_orthogonal:
            combos: List[Dict[str, Any]] = []

            def _recurse(i: int, cur: Dict[str, Any]):
                nonlocal combos
                if i >= len(spec.multi_factor_orthogonal):
                    combos.append(copy.deepcopy(cur)); return
                f = spec.multi_factor_orthogonal[i]
                for lvl in f.levels:
                    clone = copy.deepcopy(cur)
                    apply_patches(clone, [{"path": f.path, "value": lvl if f.mode == "absolute" else (lvl)}])
                    _recurse(i+1, clone)

            _recurse(0, copy.deepcopy(base_portfolio))
            for c in combos:
                results["multi"].append(calculate_callback(c) if calculate_callback else c)

        return results

# Minimal helper to read a path value (best effort); not full JSONPath
def eval_path(obj: Dict[str, Any], path: str):
    try:
        from ..utils.jsonpath_patch import _parse_path
        cur = obj
        for key, idx in _parse_path(path):
            if idx is None:
                if key is None: return None
                cur = cur.get(key, None)
            else:
                if key is None:
                    cur = cur[idx]
                else:
                    cur = cur.get(key, [])[idx]
            if cur is None: return None
        return cur
    except Exception:
        return None
