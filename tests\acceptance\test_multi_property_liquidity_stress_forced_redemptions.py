import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import (
    Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal, MFHoldingLot
)
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger,
    PropertyOwnershipCosts, PropertyOccupancy
)
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD

def _ctx():
    class _Cfg:
        content = {
            "cii": {"FY2024-25": 348},
            # MF tax rules so redemptions are logged with ST/LT
            "mf_tax": {
                "equity": {
                    "holding_days_ltcg": 365, "stcg_rate_pct": 15,
                    "ltcg_rate_pct": 10, "ltcg_annual_exemption": 100000,
                    "indexation_allowed": False
                }
            }
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_multi_property_forced_redemptions_and_risk_flags():
    """
    Two UC properties with large concurrent demands. Liquid cash is small.
    Expect: MF redemptions (FIFO), OD draw, and possibly HL Top-up.
            Risk flags should include OD ≥80% and HL Top-up usage if tapped.
    """
    ctx = _ctx()

    # P1 and P2: large demands in the same window
    ev1 = PaymentEvent(
        name="P1 Possession 70%", stage="Possession",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=12),
        percent_of_base=DD(70), gst_pct=DD(0), financeable_by_bank=True
    )
    P1 = Property(
        id="P1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(10_000_000), ltv_pct=DD(70)),
        plan=PaymentPlan(events=[ev1]),
        ownership_costs=PropertyOwnershipCosts(), occupancy=PropertyOccupancy()
    )

    ev2 = PaymentEvent(
        name="P2 Possession 80%", stage="Possession",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=12),
        percent_of_base=DD(80), gst_pct=DD(0), financeable_by_bank=True
    )
    P2 = Property(
        id="P2", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(8_000_000), ltv_pct=DD(70)),
        plan=PaymentPlan(events=[ev2]),
        ownership_costs=PropertyOwnershipCosts(), occupancy=PropertyOccupancy()
    )

    # MF stash: two lots to exercise FIFO
    lot_old = MFHoldingLot(id="EQ-OLD", scheme_name="EQ", asset_type="equity",
                           units=1000, nav_acquired=50, acquired_date="2023-01-01",
                           current_nav=100, exit_load_pct=DD(0))
    lot_new = MFHoldingLot(id="EQ-NEW", scheme_name="EQ", asset_type="equity",
                           units=800, nav_acquired=90, acquired_date="2025-03-01",
                           current_nav=95, exit_load_pct=DD(0))

    person = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(300_000))

    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=18,
        persons=[person], properties=[P1, P2], mf_holdings=[lot_old, lot_new],
        user_starting_liquid_cash_by_person={"u": DD(300_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(
            od_limit=DD(1_500_000), od_annual_rate_pct=DD(12),
            mf_fifo_enabled=True, allow_hl_topup=True
        ),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
    res = eng.result()
    ledger = res["ledger"]

    # MF redemptions should occur (funding_mf category)
    assert any(l for l in ledger if l.get("category") == "funding_mf"), "Expected MF FIFO redemptions"

    # OD draw & interest likely (but depends on disbursal), check risk flags at least
    flags = res.get("risk_flags", [])
    # At least the OD utilisation flag should appear in stress
    assert any("OD utilisation reached ≥80%" in f for f in flags), "Expected OD ≥80% risk flag"
    # If HL Top-up used, a specific risk flag should be emitted
    if any("HL Top-up" in l.get("label","") for l in ledger):
        assert any("Relied on HL Top-up" in f for f in flags), "Expected HL Top-up reliance flag when used"
