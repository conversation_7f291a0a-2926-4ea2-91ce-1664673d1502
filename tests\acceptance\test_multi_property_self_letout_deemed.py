import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Port<PERSON>lio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.types import D as DD

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_multi_property_self_letout_deemed_if_supported():
    """
    Scenario:
      - P1 self-occupied (primary)
      - P2 let-out
      - P3 extra residential → deemed let-out if engine supports that rule
    Assertions:
      - rent_received entries for P2
      - deemed rent for P3 only if rule is implemented (else skip)
    """
    ctx = _ctx()

    p1 = Property(
        id="SO1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(8_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(annual_property_tax=DD(10000)),
        occupancy=PropertyOccupancy(move_in_month=0)  # self-occupied
    )
    p2 = Property(
        id="LO1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(12_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(annual_property_tax=DD(15000)),
        occupancy=PropertyOccupancy(let_out_month=0, rent_yield_pct_of_value=DD(2.8))
    )
    p3 = Property(
        id="DM1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(9_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(annual_property_tax=DD(12000)),
        occupancy=PropertyOccupancy()  # extra; if engine supports deemed let-out, rent might be imputed
    )
    person = Person(id="u", name="User", tax_regime="old", shareholdings={"SO1": 100, "LO1": 100, "DM1": 100}, liquid_cash=DD(200_000))
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
        persons=[person], properties=[p1, p2, p3], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(200_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
    res = eng.result()
    ledger = res["ledger"]

    # Let-out P2 should have rent_received lines
    assert any(l for l in ledger if l.get("category") == "rent_received" and l.get("property_id") == "LO1"), \
        "Expected rent from let-out property P2"

    # Deemed let-out for P3 — only if the engine models it
    deemed_lines = [l for l in ledger if "deemed" in l.get("category","").lower() and l.get("property_id") == "DM1"]
    if not deemed_lines:
        pytest.skip("Deemed let-out not modeled in this build")
