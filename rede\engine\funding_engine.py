from __future__ import annotations

from datetime import date
from decimal import Decimal
from typing import Dict, List, Tuple

from ..engine.context import EngineContext
from ..engine.ledger import Ledger, LedgerLine
from ..models.core import MFHoldingLot
from ..models.property import Property
from ..types import D, Money
from ..utils.money import pct_to_decimal, qrupee


class FundingEngine:
    """
    Funding waterfall orchestrator (simple and predictable):

        1) Liquid cash
        2) Mutual Funds (FIFO redemption, proceeds are NET of exit load)
        3) OD (within limit; interest is accrued and posted next month)
        4) HL Top-up (conservative: within LTV headroom; not guaranteed)

    Also keeps liquidity KPIs:
        - Minimum liquid (₹) & the month it occurred
        - Peak OD (₹) & the month it occurred

    And it logs MF redemptions for TaxEngine (so FY CGT can be settled later).

    Design notes:
    - OD interest for month 't' is *accrued* at month end and posted as an
      outflow in month 't+1' (auditor-friendly separation).
    - We do *not* credit monthly returns here; the TreasuryEngine handles
      idle liquid accruals and other buckets; this class is strictly
      "how do we fund a shortfall this month".
    """

    def __init__(self, ctx: EngineContext):
        self.ctx = ctx
        pf = ctx.portfolio

        # ------------- Running balances -------------
        # Pooled liquid: sum of all persons' starting cash (None -> 0)
        self.liquid_bal: Decimal = sum(
            (D(v) for v in (pf.user_starting_liquid_cash_by_person or {}).values()), D(0)
        )

        # OD principal outstanding; interest is accrued separately (see below)
        self.od_bal: Decimal = D(0)
        # Interest accrued this month to be posted next month (as an outflow)
        self.od_interest_due_next_month: Decimal = D(0)

        # ------------- KPI trackers -------------
        self.min_liquid: Decimal = self.liquid_bal
        self.min_liquid_month: int = 0
        self.peak_od: Decimal = D(0)
        self.peak_od_month: int = 0

        # ------------- MF FIFO stack -------------
        # Keep lots sorted by acquired_date so redemptions are FIFO
        self.mf_fifo: List[MFHoldingLot] = sorted(pf.mf_holdings, key=lambda x: x.acquired_date)
        # (lot_id, units_sold, proceed_gross, exit_load, posting_date)
        self.mf_redemptions_log: List[Tuple[str, float, Decimal, Decimal, date]] = []

        # Exposed so Timeline can set it each month (used for horizon close-out redemptions)
        self.current_month_index: int = 0

    # ============================================================
    #                       KPI helpers
    # ============================================================

    def _note_min_liquid(self, month_index: int) -> None:
        """
        If liquid dipped to a new low this month, remember it.
        """
        if self.liquid_bal < self.min_liquid:
            self.min_liquid = self.liquid_bal
            self.min_liquid_month = month_index

    def _note_peak_od(self, month_index: int) -> None:
        """
        If OD climbed to a new high this month, remember it.
        """
        if self.od_bal > self.peak_od:
            self.peak_od = self.od_bal
            self.peak_od_month = month_index

    # ============================================================
    #                      Month boundaries
    # ============================================================

    def begin_month(self, posting: date) -> Decimal:
        """
        At the very start of the month, return the OD interest that was
        accrued LAST month so the caller can post it as an outflow now.

        After returning it, we clear the carry bucket.
        """
        due = self.od_interest_due_next_month
        self.od_interest_due_next_month = D(0)
        return due

    def end_month_accrue_od_interest(self) -> None:
        """
        At the end of the month, compute OD interest on today's OD balance
        and carry it to 'od_interest_due_next_month'. The poster (Timeline)
        will recognize it next month in begin_month().
        """
        if self.od_bal <= 0:
            return
        # Convert annual % to monthly decimal (simple 12ths — OD is revolving/short-term)
        od_rate_m = pct_to_decimal(self.ctx.portfolio.funding_strategy.od_annual_rate_pct) / D(12)
        interest = self.od_bal * od_rate_m
        if interest > 0:
            # Quantize to rupees (no paise drift)
            self.od_interest_due_next_month += qrupee(interest)

    # ============================================================
    #                         Surplus
    # ============================================================

    def add_surplus(self, month_index: int, amount: Decimal) -> None:
        """
        Add a positive surplus to liquid (e.g., coupons, rent, net month surplus).
        Note: min_liquid only ever decreases, so no KPI change here.
        """
        self.liquid_bal += amount

    # ============================================================
    #                       Waterfall core
    # ============================================================

    def cover_shortfall(
        self,
        month_index: int,
        posting: date,
        shortfall: Decimal,
        ledger: Ledger,
        bank_paid_so_far: Dict[str, Decimal],
        outstanding: Dict[str, Decimal],
        properties: List[Property],
    ) -> None:
        """
        Execute the waterfall to cover 'shortfall' this month.

        Order: Liquid -> MF FIFO -> OD -> HL Top-up

        Side effects:
          - Writes ledger lines for each funding action (positive 'amount' = inflow)
          - Updates: self.liquid_bal, self.od_bal
          - Updates per-property: 'outstanding' and 'bank_paid_so_far' for HL top-up
          - Logs MF redemptions for CGT (TaxEngine will settle FY later)
        """
        need = Decimal(shortfall)

        # 1) Liquid
        use_liq = min(self.liquid_bal, need)
        if use_liq > 0:
            self.liquid_bal -= use_liq
            ledger.lines.append(LedgerLine(
                date=posting,
                label="Funding from Liquid",
                category="funding_liquid",
                amount=Money(use_liq),        # inflow to cover shortfall
                reason="Cover shortfall from available cash"
            ))
            need -= use_liq
            # if liquid fell to a new low, capture it
            self._note_min_liquid(month_index)

        if need <= 0:
            return

        # 2) Mutual Funds (FIFO)
        # Only if enabled, we still need funds, and we have lots left
        if self.ctx.portfolio.funding_strategy.mf_fifo_enabled and self.mf_fifo and need > 0:
            need = self._redeem_mf_fifo(month_index, posting, need, ledger)

        if need <= 0:
            return

        # 3) OD draw (respect limit; if limit is 0, OD is "off")
        if need > 0:
            od_limit = D(self.ctx.portfolio.funding_strategy.od_limit or 0)
            if od_limit > 0:
                headroom = max(D(0), od_limit - self.od_bal)
                draw = min(headroom, need)
                if draw > 0:
                    self.od_bal += draw
                    ledger.lines.append(LedgerLine(
                        date=posting,
                        label="OD Draw",
                        category="funding_od",
                        amount=Money(draw),     # inflow (we draw OD to fund shortfall)
                        reason="Cover residual shortfall via OD (within limit)"
                    ))
                    need -= draw
                    self._note_peak_od(month_index)

        if need <= 0:
            return

        # 4) HL Top-up (conservative LTV headroom)
        # We iterate properties to see where a top-up is still possible.
        for prop in properties:
            if prop.loan is None:
                continue
            # Lazy import to avoid circular import at module import time
            from .loan_engine import LoanEngine
            le = LoanEngine()

            # Max permissible loan value (LTV rule) minus what the bank has already disbursed
            max_loan = le.compute_max_loan_cap(prop)
            headroom = max(Decimal("0"), max_loan - bank_paid_so_far.get(prop.id, D(0)))
            if headroom <= 0:
                continue

            topup = min(headroom, need)
            if topup > 0:
                # Treat as a new disbursal: both outstanding and bank_paid_so_far go up
                outstanding[prop.id] = outstanding.get(prop.id, D(0)) + topup
                bank_paid_so_far[prop.id] = bank_paid_so_far.get(prop.id, D(0)) + topup

                ledger.lines.append(LedgerLine(
                    date=posting,
                    label="HL Top-up",
                    category="funding_hl_topup",
                    amount=Money(topup),       # inflow (bank funds)
                    property_id=prop.id,
                    reason="Top-up within LTV headroom to cover shortfall"
                ))

                # NEW: ask Timeline to recompute EMI from next month (bank keeps tenure, increases EMI)
                try:
                    setattr(prop.loan, "_recalc_emi_from_next_month", "increase_emi")
                except Exception:
                    pass

                # Soft risk flag (UI/narrative) — bank’s approval is not guaranteed
                try:
                    self.ctx.timeline.risk_flags.append(
                        f"Relied on HL Top-up of ₹{float(topup):,.0f} — non-guaranteed source; bank approval required."
                    )
                except Exception:
                    # If ctx.timeline isn't set, just skip the narrative hook
                    pass

                need -= topup
                if need <= 0:
                    break

        # If 'need' is still > 0 here, all sources were exhausted.
        # The deficit will appear as negative net cash for the month (correct).
        return

    # ============================================================
    #                  Mutual Fund FIFO redemption
    # ============================================================

    def _redeem_mf_fifo(self, month_index: int, posting: date, need: Decimal, ledger: Ledger) -> Decimal:
        """
        Sell MF units FIFO until 'need' is covered by NET proceeds (after exit load).

        - We DO NOT compute CGT here; we merely log (lot_id, units, gross, exit_load, date)
          and let TaxEngine settle at FY-boundary (or at horizon close-out).
        - Proceeds are added as positive ledger inflows.
        - Returns the *remaining* unmet need (>= 0).
        """
        remaining = need
        new_fifo: List[MFHoldingLot] = []

        for lot in self.mf_fifo:
            if remaining <= 0:
                # No more funding required; keep the lot as-is
                new_fifo.append(lot)
                continue

            # Net per unit after exit load, e.g., NAV * (1 - exit_load%)
            per_unit_net = Decimal(str(lot.current_nav)) * (Decimal(1) - pct_to_decimal(lot.exit_load_pct))
            if per_unit_net <= 0:
                # Skip pathological lots (no net value)
                new_fifo.append(lot)
                continue

            # Units needed rounded to 0.0001 (reasonable MF precision)
            units_needed = (remaining / per_unit_net).quantize(Decimal("0.0001"))
            units_to_sell = min(Decimal(str(lot.units)), units_needed)
            if units_to_sell <= 0:
                new_fifo.append(lot)
                continue

            gross = Decimal(str(lot.current_nav)) * units_to_sell
            exit_load = gross * pct_to_decimal(lot.exit_load_pct)
            net = gross - exit_load  # cash we actually receive

            # Post ledger inflow for net proceeds
            ledger.lines.append(LedgerLine(
                date=posting,
                label=f"MF Redemption - {lot.scheme_name}",
                category="funding_mf",
                amount=Money(net),
                reason="FIFO redemption to fund shortfall",
                formula_hint="units*nav - exit_load"
            ))

            # Log for CGT at FY settlement / horizon close-out
            self.mf_redemptions_log.append((lot.id, float(units_to_sell), gross, exit_load, posting))

            # Narrative KPI: accumulate total MF cash used (for later display)
            try:
                if not hasattr(self.ctx, "_mf_usage_total"):
                    setattr(self.ctx, "_mf_usage_total", D(0))
                self.ctx._mf_usage_total = getattr(self.ctx, "_mf_usage_total") + net
            except Exception:
                pass

            # Reduce the lot (or drop if fully sold)
            lot_units_left = Decimal(str(lot.units)) - units_to_sell
            if lot_units_left > 0:
                # shallow copy lot but with fewer units remaining
                lot_copy = MFHoldingLot(**dict(lot))
                lot_copy.units = float(lot_units_left)
                new_fifo.append(lot_copy)

            # Reduce remaining need by the cash we just raised
            remaining -= net

        # Update FIFO with any remaining lots
        self.mf_fifo = new_fifo
        return remaining

    # ============================================================
    #                    Public helpers (MF)
    # ============================================================

    def redeem_mf_amount(self, month_index: int, posting: date, amount: Decimal, ledger: Ledger) -> Decimal:
        """
        Try to raise *amount* from MF FIFO; returns the UNMET remainder (>= 0).
        Used by Treasury/horizon code if they need a targeted MF redemption.
        """
        need = Decimal(amount)
        if need <= 0 or not self.mf_fifo:
            return D(0)
        return self._redeem_mf_fifo(month_index, posting, need, ledger)

    def redeem_all_mf(self, posting: date, ledger: Ledger) -> None:
        """
        Force redemption of ALL remaining MF lots — used at horizon close-out
        so short-horizon decisions reflect realized, post-tax outcomes.

        (Tax still settles at FY boundary or immediately after horizon,
         handled in Timeline/TaxEngine.)
        """
        idx = getattr(self, "current_month_index", 0)
        sentinel = D("1e18")  # very large 'need' to drain the queue
        while self.mf_fifo:
            remaining = self._redeem_mf_fifo(idx, posting, sentinel, ledger)
            if remaining == sentinel:  # no progress; defensive break
                break
