import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal, PlannedTopUp
from rede.models.property import Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger, PropertyOwnershipCosts, PropertyOccupancy
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_planned_topups_reduce_or_eliminate_od_interest():
    """
    Goal: Show that planned top-ups (e.g., bonuses) arriving around big demands
    can prevent OD usage/interest.

    We run two portfolios:
      A) With planned top-up at m=12 that covers the large demand at m=12
      B) Without planned top-up (same OD limit)
    Expected:
      - Portfolio A ledger has fewer/no 'funding_od_interest' lines around m=12 compared to B
    """
    ctx = _ctx()

    # Confirm model exposes PlannedTopUp in FundingStrategy; else skip gracefully
    if PlannedTopUp is None or not hasattr(FundingStrategy, "planned_topups"):
        pytest.skip("Planned top-ups not supported in this build")

    big_ev = PaymentEvent(
        name="Big Demand", stage="Possession",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=12),
        percent_of_base=DD(60), gst_pct=DD(0), financeable_by_bank=False
    )
    prop = Property(
        id="INV-PT", city="Pune",
        heads=PropertyHeads(base_price_ex_gst=DD(5_000_000)),
        plan=PaymentPlan(events=[big_ev]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    person = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(200_000))

    # A) WITH planned top-up at m=12 (e.g., 20L)
    pfA = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=18,
        persons=[person], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(200_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(
            od_limit=DD(1_500_000), od_annual_rate_pct=DD(12), mf_fifo_enabled=False,
            planned_topups=[PlannedTopUp(month=12, amount=DD(2_000_000), person_id="u")]
        ),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    # B) WITHOUT planned top-up (identical otherwise)
    pfB = Portfolio.parse_obj({**pfA.dict(), "funding_strategy": {**pfA.funding_strategy.dict(), "planned_topups": []}})

    # Run both
    for pf in (pfA, pfB):
        eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
        result = eng.result()
        pf._test_result = result  # stash for assertions

    # Compare OD interest occurrences near m=12
    odA = [l for l in pfA._test_result["ledger"] if l.get("category") == "funding_od_interest" and 12 <= l["month_index"] <= 13]
    odB = [l for l in pfB._test_result["ledger"] if l.get("category") == "funding_od_interest" and 12 <= l["month_index"] <= 13]

    # With top-up we expect fewer (or none) OD interest postings
    assert len(odA) <= len(odB), "Planned top-ups should reduce OD interest occurrences around the demand month"
