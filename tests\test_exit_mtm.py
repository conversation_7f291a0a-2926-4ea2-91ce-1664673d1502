from datetime import date
from decimal import Decimal as D
from rede.engine.exit_engine import ExitEngine
from rede.engine.tax_engine import TaxEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person
from rede.models.property import Property, PropertyHeads, ExitSettings
from rede.types import D as DD

def test_mtm_includes_brokerage_cgt_penalties():
    # Minimal portfolio & tax engine (with a trivial CII)
    p = Person(id="u", name="User", tax_regime="old", shareholdings={"P1": 100}, liquid_cash=DD(0))
    prop = Property(id="P1", city="BLR", heads=PropertyHeads(base_price_ex_gst=DD(0)),
                    exit=ExitSettings(brokerage_pct=DD(1.0), buyer_tds_pct=DD(1.0)))
    pf = Portfolio(currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
                   persons=[p], properties=[prop], mf_holdings=[],
                   user_starting_liquid_cash_by_person={"u": DD(0)},
                   alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
                   funding_strategy=None, tax_settings_global=None, configs=None)

    class _Cfg:
        content = {"property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730}, "cii": {}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    tax = TaxEngine(ctx, pf)
    ex = ExitEngine()

    current_value = D(1_000_000)
    outstanding = D(200_000)
    # Seed acquisition cost cache (resale/existing path)
    prop._acq_total_cached = D(700_000)

    mtm = ex.mtm_net_value(t=6, prop=prop, current_value=current_value,
                           outstanding_principal=outstanding, include_cgt=True, tax_engine=tax)
    # Basic sanity: MTM net after loan must be less than current_value - outstanding,
    # because brokerage + buyer TDS + CGT reduce it.
    assert mtm["mtm_net_after_loan"] < float(current_value - outstanding)
