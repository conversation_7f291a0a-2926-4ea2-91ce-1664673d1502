import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.types import D as DD

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_uc_resale_transfer_two_persons_if_supported():
    """
    Scenario: Person A books UC property at m=0, sells to Person B at m=18.
    Expect:
      - Person A ledger shows sale, capital gains (short term)
      - Person B ledger shows fresh entry, stamp duty, GST, loan funding
    """
    ctx = _ctx()

    if not hasattr(Property, "transfer_events"):
        pytest.skip("Resale/transfer feature not wired")

    # Base property
    prop = Property(
        id="UCX", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(4_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )

    pA = Person(id="A", name="Alice", tax_regime="old", shareholdings={}, liquid_cash=DD(500_000))
    pB = Person(id="B", name="Bob", tax_regime="old", shareholdings={}, liquid_cash=DD(500_000))

    # Transfer event: resale at m=18 for +10% markup
    try:
        prop.transfer_events = [{"month": 18, "from": "A", "to": "B", "price_markup_pct": 10, "stamp_duty_pct": 6}]
    except Exception:
        pytest.skip("transfer_events attribute not assignable")

    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=36,
        persons=[pA, pB], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"A": DD(500_000), "B": DD(500_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
    ledger = eng.result()["ledger"]

    seller = [l for l in ledger if l.get("person")=="A"]
    buyer  = [l for l in ledger if l.get("person")=="B"]

    if not seller or not buyer:
        pytest.skip("No split ledger by person → resale feature not firing")
    assert any("sale" in l.get("category","").lower() for l in seller)
    assert any("stamp" in l.get("category","").lower() for l in buyer)
