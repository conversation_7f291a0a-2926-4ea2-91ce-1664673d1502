from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Port<PERSON>lio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.types import D as DD

def _ctx_tax():
    class _Cfg:
        content = {
            "tds_rules": {"rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}},
            "ihp": {"self_occupied_interest_cap_per_person": 200000, "hp_loss_setoff_cap_per_person": 200000, "carry_forward_years": 8},
            "cii": {"FY2024-25": 348}
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_joint_owners_letout_allocation_panel():
    ctx = _ctx_tax()
    a = Person(id="a", name="A", tax_regime="old", shareholdings={"P1": 50}, liquid_cash=DD(0))
    b = Person(id="b", name="B", tax_regime="old", shareholdings={"P1": 50}, liquid_cash=DD(0))

    prop = Property(
        id="P1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(10_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(property_tax_annual=DD(50_000)),
        occupancy=PropertyOccupancy(let_out_month=0, rent_yield_pct_of_value=DD(3.0))
    )

    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
        persons=[a, b], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"a": DD(0), "b": DD(0)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf
    eng.run()
    panel = eng.result().get("tax_panel", {})
    # We only assert panel presence and that let-out HP totals exist (engine splits internally)
    assert "ihp" in panel and isinstance(panel["ihp"].get("letout_net_hp_total", 0.0), float)
