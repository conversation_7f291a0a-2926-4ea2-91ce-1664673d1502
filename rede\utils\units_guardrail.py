from __future__ import annotations
from decimal import Decimal
from ..types import D

def _looks_like_decimal_rate(x: Decimal) -> bool:
    # E.g. 0.06 where user intended 6%
    try:
        v = D(x)
        return D("0") < v <= D("1")
    except Exception:
        return False

def _fix_pct(obj, attr: str, risk_flags: list[str], label: str):
    try:
        v = getattr(obj, attr, None)
        if v is None:
            return
        if _looks_like_decimal_rate(v):
            setattr(obj, attr, float(D(v) * D(100)))
            risk_flags.append(f"Interpreted '{label}' as {float(v)*100:.2f}% (user entered {v}).")
        # Soft clamp crazy values
        if float(getattr(obj, attr)) < 0:
            setattr(obj, attr, 0.0)
            risk_flags.append(f"Clamped negative '{label}' to 0%.")
        if float(getattr(obj, attr)) > 1000:
            setattr(obj, attr, 100.0)
            risk_flags.append(f"Clamped '{label}' above 1000% to 100%.")
    except Exception:
        pass

def apply_units_guardrail(portfolio, risk_flags: list[str]):
    """
    Scan common percentage fields and auto-fix decimal-vs-percent mistakes.
    Add gentle flags rather than errors.
    """
    # Portfolio-level
    _fix_pct(portfolio, "alt_invest_return_after_tax_pct", risk_flags, "Alt-invest return")
    if getattr(portfolio, "funding_strategy", None):
        _fix_pct(portfolio.funding_strategy, "od_annual_rate_pct", risk_flags, "OD annual rate")

    # Properties
    for p in getattr(portfolio, "properties", []) or []:
        occ = getattr(p, "occupancy", None)
        if occ:
            _fix_pct(occ, "annual_rent_escalation_pct", risk_flags, f"{p.id} rent escalation")
            _fix_pct(occ, "vacancy_pct", risk_flags, f"{p.id} vacancy")
            _fix_pct(occ, "collection_efficiency_pct", risk_flags, f"{p.id} collection efficiency")
            _fix_pct(occ, "property_manager_fee_pct_of_rent", risk_flags, f"{p.id} PM fee")
            _fix_pct(occ, "rent_yield_pct_of_value", risk_flags, f"{p.id} rent yield")

        pm = getattr(p, "price_model", None)
        if pm:
            _fix_pct(pm, "base_annual_cagr_pct", risk_flags, f"{p.id} base CAGR")

        oc = getattr(p, "ownership_costs", None)
        if oc:
            _fix_pct(oc, "annual_maintenance_escalation_pct", risk_flags, f"{p.id} maint escalation")
            _fix_pct(oc, "insurance_escalation_pct", risk_flags, f"{p.id} insurance escalation")

    return portfolio
