from pydantic import BaseModel, <PERSON>, root_validator, validator
from typing import Literal
from datetime import date
from ..types import Money, Percent, D
from .enums import PaymentTriggerType  # align with tests

class Subvention(BaseModel):
    payer: Literal["builder"] = "builder"
    coverage: Literal["pre_emi", "emi"] = "pre_emi"
    end_trigger: Literal["possession", "oc_received", "month"] | None = None
    end_month: int | None = None

class PaymentTrigger(BaseModel):
    """
    Minimal trigger used across tests: type + optional month offset.
    Extend for possession/OC/handover if needed (engine already supports gating).
    """
    type: PaymentTriggerType
    month: int | None = None  # only for type == "month"

class PaymentEvent(BaseModel):
    name: str
    stage: str | None = None
    trigger: PaymentTrigger

    # Amount based on a selected base; tests commonly use percent_of_base
    percent_of_base: Percent
    gst_pct: Percent = D(0)
    financeable_by_bank: bool = True
    subvention: Subvention | None = None

    due_by_day: int = Field(default=5, ge=1, le=28)

class StageCap(BaseModel):
    stage_name: str
    max_pct_of_agreement_ex_gst: Percent

class PaymentPlan(BaseModel):
    events: list[PaymentEvent] = Field(default_factory=list)


    @root_validator
    def _sum_to_100(cls, v):
        ev = v.get('events', [])
        total = sum((getattr(e, 'percent_of_base', 0) or 0) for e in ev)
        if abs(float(total) - 100.0) > 0.5:
            raise ValueError('PaymentPlan: sum(percent_of_base) must be ~100%')
        return v
    @validator('events', each_item=True)
    def _subvention_valid(cls, e):
        sb = getattr(e, 'subvention', None)
        if sb and getattr(sb, 'end_trigger', None) == 'month' and getattr(sb, 'end_month', None) in (None, 0):
            raise ValueError("Subvention end_trigger='month' requires end_month")
        return e
