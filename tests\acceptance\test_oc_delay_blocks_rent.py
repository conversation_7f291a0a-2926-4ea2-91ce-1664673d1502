import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import <PERSON><PERSON><PERSON>, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.types import D as DD

def _ctx():
    class _Cfg:
        content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_oc_delay_blocks_rent_until_legal_oc():
    """
    Goal: If OC (Occupancy Certificate) arrives later than possession/let-out intent,
    rent MUST NOT start before OC.

    We set:
      - let_out_month=20 (user intent to rent early)
      - legal.oc_month=28 (OC arrives later)
    Expected:
      - No "Rent Received" entries before OC month
      - Rent starts only after OC month
    """
    ctx = _ctx()

    # Build property with an explicit legal OC month if the model supports it
    prop = Property(
        id="INV-OC", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(8_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(let_out_month=20)
    )

    # Try to set legal OC month if the property has `.legal` model
    # (Some deployments expose Property.legal.oc_month; if absent, we skip)
    if not hasattr(prop, "legal"):
        pytest.skip("Property.legal not available in this build (skipping OC gating test)")
    if not hasattr(prop.legal, "oc_month"):
        pytest.skip("Property.legal.oc_month not available (skipping OC gating test)")

    prop.legal.oc_month = 28  # OC arrives later than user attempts to let out

    p = Person(id="u", name="Owner", tax_regime="old", shareholdings={}, liquid_cash=DD(200_000))
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=36,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(200_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf
    eng.run()
    ledger = eng.result()["ledger"]

    # Ensure no rent received BEFORE OC
    pre_oc = [l for l in ledger if l.get("category") == "rent_received" and l["month_index"] < 28]
    assert not pre_oc, "Rent should not be posted before OC month"

    # Ensure rent received after OC (if yield/valuation implies rent > 0)
    post_oc = [l for l in ledger if l.get("category") == "rent_received" and l["month_index"] >= 28]
    assert post_oc, "Rent should start only after OC month"
