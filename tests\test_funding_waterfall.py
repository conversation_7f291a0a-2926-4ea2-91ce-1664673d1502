import datetime as dt
from decimal import Decimal as D

from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger,
    PropertyOwnershipCosts, PropertyOccupancy
)
from rede.models.loans import TermLoan, LoanInsurance, RateIndex
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD


def _ctx_basic():
    class _Cfg:
        content = {
            "tds_rules": {"rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}},
            "mf_tax": {"equity": {"holding_days_ltcg": 365, "stcg_rate_pct": 15, "ltcg_rate_pct": 10,
                                  "ltcg_annual_exemption": 100000, "indexation_allowed": False}},
            "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
            "cii": {"FY2024-25": 348, "FY2025-26": 360}
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx


def test_risk_flag_od_utilisation_and_hl_topup():
    ctx = _ctx_basic()

    # Person has zero cash, no MF holdings → forces funding waterfall.
    p = Person(id="p1", name="Owner", tax_regime="old", shareholdings={}, liquid_cash=DD(0))

    # Event at t=0 demanding ₹90 with od_limit=100 → utilisation 90% triggers OD≥80% flag.
    ev0 = PaymentEvent(
        name="Small Demand (OD Stress)",
        stage="Booking",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
        percent_of_base=DD(100), gst_pct=DD(0), financeable_by_bank=False
    )
    prop_od = Property(
        id="propOD", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(90)),
        plan=PaymentPlan(events=[ev0]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        loan=None
    )

    # Separate property to exercise HL Top-up.
    # Create demand of ₹300, od_limit=0; allow_hl_topup=True; Loan exists with large headroom.
    ev1 = PaymentEvent(
        name="Demand (Top-up)",
        stage="Booking",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
        percent_of_base=DD(100), gst_pct=DD(0), financeable_by_bank=False
    )
    loan = TermLoan(
        id="L1", sanctioned_amount=DD(0), tenure_months=240,
        initial_roi_annual_pct=DD(9), spread_pct=DD(0), rate_index=RateIndex.FIXED,
        on_rate_change_policy="ADJUST_EMI", ltv_basis=None, rate_shocks=[]
    )
    prop_hl = Property(
        id="propHL", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(1_000_000)),  # large agreement base → ample LTV headroom
        plan=PaymentPlan(events=[ev1]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        loan=loan
    )

    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=2,
        persons=[p],
        properties=[prop_od, prop_hl],
        mf_holdings=[],
        user_starting_liquid_cash_by_person={"p1": DD(0)},
        alt_invest_return_after_tax_pct=DD(6),
        loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(
            od_limit=DD(100), od_annual_rate_pct=DD(12),
            mf_fifo_enabled=False, allow_hl_topup=True
        ),
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf
    eng.run()
    res = eng.result()

    # Risk flags strengthened assertions:
    flags = res.get("risk_flags", [])
    assert any("OD utilisation reached ≥80%" in f for f in flags), "Expected OD utilisation risk flag"
    assert any("Relied on HL Top-up" in f for f in flags), "Expected HL Top-up risk flag"
