import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.models.loans import TermLoan, LoanInsurance, RateIndex, RateChangePolicy
from rede.types import D as DD

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_mrta_upfront_bundled_increases_financed_principal_vs_annual():
    """
    Goal: Compare MRTA 'UpfrontBundled' vs 'Annual' premium.
      - UpfrontBundled should increase financed principal (and typically EMI),
        and create a single upfront insurance outflow (or bundled into loan).
      - Annual should show recurring premium outflows; principal unaffected.
    Assertions:
      - EMI (or total financed) with UpfrontBundled >= Annual case
      - Annual shows recurring 'Loan Insurance Premium' outflows in ledger
    """
    ctx = _ctx()

    # Confirm LoanInsurance exists and TermLoan accepts loan_insurance
    if LoanInsurance is None:
        pytest.skip("LoanInsurance model not available in this build")

    heads = PropertyHeads(base_price_ex_gst=DD(5_000_000))
    propA = Property(
        id="MRTA-A", city="BLR",
        heads=heads, plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(), occupancy=PropertyOccupancy()
    )
    propB = Property.parse_obj(propA.dict()); propB.id = "MRTA-B"

    # A) UpfrontBundled MRTA
    loanA = TermLoan(
        id="L1", sanctioned_amount=DD(3_500_000), tenure_months=240,
        initial_roi_annual_pct=DD(9), spread_pct=DD(0), rate_index=RateIndex.FLOATING,
        on_rate_change_policy=RateChangePolicy.ADJUST_EMI,
        loan_insurance=LoanInsurance(type="MRTA", premium_payment="UpfrontBundled", upfront_premium=DD(100_000))
    )
    propA.loan = loanA

    # B) Annual MRTA (recurring premium)
    loanB = TermLoan(
        id="L2", sanctioned_amount=DD(3_500_000), tenure_months=240,
        initial_roi_annual_pct=DD(9), spread_pct=DD(0), rate_index=RateIndex.FLOATING,
        on_rate_change_policy=RateChangePolicy.ADJUST_EMI,
        loan_insurance=LoanInsurance(type="MRTA", premium_payment="Annual", annual_premium=DD(12_000))
    )
    propB.loan = loanB

    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(400_000))

    base_pf = dict(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=24,
        persons=[p.dict()], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": str(DD(400_000))},
        alt_invest_return_after_tax_pct=str(DD(6)), loan_sanction_risk_pct=str(DD(0)),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False).dict(),
        tax_settings_global=TaxSettingsGlobal().dict(), configs=RuntimeConfigs("","","","","").dict()
    )

    pfA = Portfolio.parse_obj({**base_pf, "properties": [propA.dict()]})
    pfB = Portfolio.parse_obj({**base_pf, "properties": [propB.dict()]})

    # Run A
    engA = TimelineEngine(ctx); engA.ctx.portfolio = pfA; engA.run()
    resA = engA.result()
    # Run B
    engB = TimelineEngine(ctx); engB.ctx.portfolio = pfB; engB.run()
    resB = engB.result()

    # Compare EMI presence (or financed principal effect)
    # We avoid exact EMI numbers; instead assert that total outflows in A are
    # at least as high as in B due to bundled premium increasing financed cost.
    totA = resA["kpis"]["total_outflows"]; totB = resB["kpis"]["total_outflows"]
    assert totA >= totB

    # Annual premium case should show recurring insurance outflows
    annual_lines = [l for l in resB["ledger"] if "Insurance" in l["label"] or "Loan Insurance" in l["label"]]
    assert annual_lines, "Expected recurring insurance premium outflows in Annual MRTA case"
