from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger, PropertyOwnershipCosts, PropertyOccupancy
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD

def test_required_buffer_at_start():
    # Single demand of 100 at t=0; no OD/MF allowed → required_buffer ≈ 100
    ev = PaymentEvent(
        name="Booking", stage="Booking",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
        percent_of_base=DD(100), gst_pct=DD(0), financeable_by_bank=False
    )
    prop = Property(
        id="P1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(100)),
        plan=PaymentPlan(events=[ev]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(0))
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=3,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(0)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(EngineContext()); eng.ctx.portfolio = pf
    eng.run()
    req = eng.result()["kpis"].get("required_buffer", 0.0)
    assert req >= 99.0  # allow minor rounding
