# DEEP CODE ANALYSIS — Real Estate Decision Engine (REDE)

Author: Senior Software Architect (15+ yrs enterprise + Indian RE finance)
Date: 2025-08-29

---

## Section 1: Executive Summary

Overall quality: Strong architectural separation, rigorous domain modeling, and comprehensive tests. Financial math is carefully implemented with Decimal precision. Codebase is production-ready with clear extension points (Treasury, Tax, Funding).

Key metrics (observed):
- Python modules reviewed: 40+
- Engines: 8 core modules (timeline, loan, tax, funding, price, exit, treasury, ledger)
- Models: 10+ Pydantic models with validators
- Tests: 30+ files with unit/micro/acceptance coverage

Critical issues (P0):
- Security: No authentication/authorization on API; no rate limiting; stack traces exposed by default.
- Performance: Potential hot-paths in TimelineEngine (large horizons/portfolios) without caching or profiling.
- Compliance: Config-driven but lacks baked-in guardrails for RBI LTV tiers/eligibility (relies on inputs).

Strategic recommendations:
- Add API auth, rate limiting, input size caps; reduce error traces in prod.
- Introduce performance profiling; add memoization/caching and incremental recompute.
- Enrich Treasury with enterprise portfolios, liquidity buckets and constraints.

---

## Section 2: File-by-File Analysis

Notes: This section focuses on correctness, maintainability, efficiency, and domain fit. I include concrete suggestions with before/after diffs where beneficial.

### server/main.py
- Strengths: FastAPI, clean mapping, CORS hook, consistent rounding via qrupee on nested panels.
- Risk: Error traces exposed unless env var set. No auth. Synchronous request handling.
- Suggest:
  - Add bearer/JWT auth, rate limiting (slowapi), and request body size limit.
  - Make calculation asynchronous for long runs; or job queue with polling endpoint.

Example (before → after):
- Before: calculate_scenario without auth/rate-limit.
- After: Add HTTPBearer + slowapi limiter, and cap horizon.

### rede/api/contracts.py
- Well-formed Pydantic response models. Backward-compatible KPI aliases.
- Suggest: Document units on each KPI; add stricter types for some panels if UI is stable.

### rede/engine/context.py
- Clean context; config hashes handy for provenance.
- Suggest: Add frozen models or explicit immutability on hashes to avoid accidental mutation.

### rede/engine/ledger.py
- Clear, minimal ledger representation.
- Suggest: Enumerate category as Literal for stronger typing; optional source module tag for debugging.

### rede/engine/funding_engine.py
- Funding waterfall is deterministic and audited via ledger lines.
- Correct OD interest accrual to “post next month” semantics; uses qrupee to eliminate paise drift.
- MF FIFO redemption logs for TaxEngine CGT settlement — good separation.
- HL Top-up path includes soft risk flag; recalculation hint via prop.loan flag — pragmatic.
- Edge cases checked: OD limit 0, negative needs.
- Suggest:
  - Extract MF redemption math to separate helper with unit tests for rounding and precision.
  - Add guard against infinite loops in redeem_all_mf (already defensive with sentinel).
  - Consider configurable OD interest day-count (actual/365) if precision is desired.

### rede/engine/treasury.py
- Accruals on liquid and accrual-only buckets; coupons with escalation and sweep-to-liquid semantics.
- Effective monthly rate computed via (1+r)^(1/12)-1 — appropriate for compounding.
- Suggest:
  - Add taxes on coupon if required by business (currently modeled as net). Consider gross vs net toggle.
  - Consider bucket-level tax flags (e.g., debt interest taxable at slab) to distinguish accrual treatments.

### rede/engine/price_engine.py
- Value path uses CAGR + additive monthly shocks; age & legal discounts applied multiplicatively (correct per spec).
- Interiors value component uses straight-line depreciation with residual — matches spec.
- Suggest:
  - Confirm interiors depreciation should affect “value” but remain non-cash; ledger remains unaffected — consistent.
  - Optionally expose appreciation_base defaulting logic if not provided by user.

### rede/engine/loan_engine.py
- EMI formula correct; pre-EMI = outstanding * monthly rate.
- Floating rate path: honors rate_shocks; fallback derived as (initial - spread) + spread → equals initial; OK.
- Disbursal cap formula matches spec: min(demand, MaxLoan - bank_paid, StageCap*B - bank_paid).
- Suggest:
  - Enforce RBI tier cap if bank_policies are provided (currently not wired in compute_max_loan_cap).
  - Add explicit handling for AdjustEMI policy in Timeline when _recalc_emi_from_next_month set.

### rede/engine/exit_engine.py
- Realized sale: brokerage, transfer, buyer TDS, loan closure and penalty; CGT immediate or deferred — robust.
- MTM: computes net cash if sold; consistent with spec toggle to include CGT.
- Suggest:
  - Consider safe-harbor/guidance value check with config (50C/43CA/56(2)(x)).

### rede/engine/tax_engine.py (partial review)
- IHP: structures ready for preconstruction 1/5, set-off caps, carry-forward, rent TDS credits.
- MF CGT: logs redemptions; computes CGT at FY boundary; equity/debt rules via config (holding period, rates, indexation toggle) — good abstraction.
- Suggest:
  - Validate current FY tax law values via configs; ensure defaults are clearly marked non-authoritative.
  - Add tests for edge boundaries (e.g., 365 days equity boundary; LTCG exemption utilization).

### rede/engine/timeline.py (partial due to length)
- Orchestrates monthly sequence: OD interest carry-in → price path → events → loans → ownership costs → occupancy (rent) → funding waterfall → end-month OD interest → treasury accruals → FY tax settlement → horizon close-out.
- Tracks narratives, risk flags, key events. Good explainability.
- Suggest:
  - Break into submodules (e.g., timeline_rent.py, timeline_loans.py) to reduce file size and improve maintainability.
  - Add performance counters; early exits where possible; consider vectorizing parts if needed.

### rede/models/*
- Property: strong validation of appreciation_base; ExistingBasis supports resale onboarding.
- Loans: full parameterization; MRTA hooks; top-ups and balance transfer scaffolding.
- Plan: subvention validators, Plan total ~100% check.
- Treasury models: concise; leave room for future tax-tracked buckets.
- Suggest: use Literal/Enum types for category-like fields everywhere (e.g., coupon_sweep).

### rede/utils/*
- money: correct Decimal use and HALF_UP for rupees.
- xirr: robust bisection fallback; guard for sign mix; good.
- units_guardrail: helpful UX soft-fix and risk flagging; excellent.

### rede/configs/*
- loader.py and engine/config_loader.py: consistent SHA hashing and tolerant loading; good for provenance.
- Suggest: unify loader entry points to a single canonical loader to avoid drift (already mostly done).

### tests/*
- Coverage breadth is strong across scenarios: subvention, rent/vacancy, OD sweep, rate shocks, tax, exits, MF FIFO, liquidity stress.
- Suggest: add property-based tests for XIRR, EMI solver, and randomized event sequences.

---

## Section 3: Domain-Specific Issues

- Stamp duty/registration: modeled via heads and acquisition basis; ensure state_rules.json provides rates — currently not enforced programmatically.
- TDS 194-IA (purchase): captured on purchase cash outflow; no credit to buyer (correct).
- TDS 194-IB (rent): threshold and rate pulled from tax config; cash inflow net of TDS with credit recorded — correct.
- Section 24(b) caps: config-driven with carry-forward years; ensure set-off cap application order (self-occupied vs let-out) is tested.
- Pre-construction interest 1/5: bucketed per property; deducted over 5 FYs — correct per spec.
- CGT: equity/debt MF rules configurable; property CGT with indexation and 54/54F toggles supported via ExitSettings + TaxEngine.
- RBI LTV guidance: compute_max_loan_cap respects LTV%; RBI tier caps should be applied via bank policies — currently not enforced (gap).

---

## Section 4: Technical Debt & Code Quality

- Large modules (timeline.py) — refactor into cohesive submodules.
- Missing API auth/rate limiting — add immediately.
- Performance unknowns — add profiling and memory benchmarks on large inputs.
- Incomplete config enforcement — wire RBI tier caps and safe-harbor checks.
- Error surface — reduce stack traces in prod; structured logging.

Optimization ideas:
- Cache price paths per property when inputs unchanged.
- Precompute FY boundaries and indices to avoid repeated parsing.
- Use dataclass slots or pydantic performance options if necessary.

Security:
- Add JWT auth, rate limiting, and body size caps.
- Validate numeric ranges aggressively; defend against pathologically large arrays.

---

## Section 5: Enhancement Roadmap

- Treasury 2.0: tax-aware accruals by bucket type; lock-in schedules and early withdrawal penalties.
- Sensitivity Runner API: wire rede/engine/sensitivity.py to server; add async batch execution.
- CA Export: expose via API; Zip bundles with manifest and hashes.
- Compliance Engine: automatic application of state rules, RBI tiers, and safe-harbor checks; produce warnings.
- Observability: structured logs, metrics (Prometheus), tracing.
- Persistence: scenario storage and comparison; audit trails.
- UX/API: pagination for ledger; delta endpoints for live sliders.

---

## Section 6: Implementation Priority Matrix

- P0 (Immediate): API auth & rate limiting; production error handling; enforce input caps; add RBI LTV tier enforcement if bank policies provided.
- P1 (Quarter): Timeline refactor; performance profiling and caching; sensitivity runner wiring; observability stack.
- P2 (Roadmap): Treasury tax-aware buckets; compliance engine; persistence & scenario management; ML-based price/rent forecasts.

---

## Appendix: Concrete Code Suggestions

- Enforce RBI LTV tier cap in LoanEngine.compute_max_loan_cap using ctx.bank_policies if present.
- Add safe-harbor checks in ExitEngine.mtm_net_value using state_rules config thresholds.
- Harden API with JWT and slowapi rate limiting; hide trace in prod by default.



---

## Section 2b: Additional File-by-File Notes (Models/Utils)

- rede/models/legal.py: LegalProfile defaults sensible; consider adding validators for lease_years_remaining with land_tenure.
- rede/models/heads.py: Registration fixed vs percent root_validator enforces exclusivity well; add default ranges for stamp/registration based on state_rules when available.
- rede/models/occupancy.py: Good aliasing for historical fields; pm_fee precedence ensures clarity between fixed/percent/both; link_rent_yield_to_value zeroes absolute rent fields.
- rede/models/exit.py: ExitSettings captures buyer TDS and CGT timing; include toggle for include_prepayment_penalty_in_mtm if needed (doc mentions it).
- rede/models/price.py: shocks and age curve are expressive; interiors flags align with PriceEngine.
- rede/models/enums.py: Clean typed enums; align Trigger types with tests.
- rede/models/core.py: Treasury integration present; consider explicit typing for treasury_buckets contents.

- rede/utils/jsonpath_patch.py: Minimal JSONPath parser is robust for our needs; good error messages; supports growing lists.
- rede/utils/units_guardrail.py: Soft clamp >1000% to 100% is sane; keep risk_flags messages user-friendly.
- rede/utils/money.py: Add effective-monthly helper next to pct_to_monthly if needed elsewhere.
- rede/utils/xirr.py: Improve docs on bracket expansion; consider returning None for non-bracketable flows instead of raising.

---


## Section 2c: TimelineEngine — Line-by-Line Deep Analysis with Regulatory Notes

Context: Orchestrates monthly simulation (t = 0..H). Below are block-level and line-referenced comments (file: rede/engine/timeline.py).

- L101–113: OD interest from previous month is recognized at current posting date via FundingEngine.begin_month. Category 'funding_od' is appropriate; outflow signs correct. No regulatory linkage.

- L114–119 (Price path): PriceEngine.compute_month_value applies CAGR + shocks + discounts (see §4 in Doc_v1.4). No tax effect here; carry only model value for rent yield cases.

- L120–179 (Builder events & TDS 194-IA):
  - L143–149: Purchase TDS computation uses heads.tds_buy_pct (default 1%) on base amount (ex-GST) which aligns with Section 194-IA (deduction on consideration, GST excluded) — CBDT Circular No. 23/2017 clarifies GST exclusion from TDS base.
  - L150–176: Splits builder net and TDS remittance. Label 'tax' for TDS line is correct. Consider adding buyer TAN/TDS challan reference in metadata for CA export.

- Subvention gates L129–136: end at possession/OC/month — matches market conventions; advisable to note that subvention is not codified in statute; tax neutrality depends on structure (no immediate statutory reference).

- L180–250 (Loan EMI/pre-EMI and tax accruals):
  - Pre-EMI and EMI interest amounts feed TaxEngine.accrue_interest; principal feeds accrue_principal_80c post-OC. That matches Section 24(b) (interest deduction) and Section 80C (principal repayment) where occupancy/OC matters.
  - For self-occupied, set-off limits apply (Rs 2 lakh typical cap; confirm current FY via config). For let-out, interest allowed net of NAV calculation (IHP rules); ensure TaxEngine applies caps per FY (Section 24(b); Finance Act updates).

- Rent handling (later in file): Rent paid vs saved vs received; for received rent, if monthly exceeds threshold, 194-IB TDS at 5% — Section 194-IB; threshold (Rs 50,000/mo) pulled from tax config. Ensure TDS credit recorded for ITR set-off.

- FY Settlement (not shown in excerpt): At Mar-31 FY end, settle MF CGT and IHP outcomes. For MF CGT: equity LTCG 10% beyond Rs 1 lakh exemption (Proviso to Section 112A) and no indexation; debt fund rules depend on post-2023 changes; configs must reflect prevailing law.

- Exit handling (not shown in excerpt): Delegates to ExitEngine.compute realized sale and MTM per toggles; Section 194-IA buyer TDS on sale is modeled as buyer deduction — correct side for seller; CGT classification STCG/LTCG by holding period; indexation toggle per property CGT config.

Caveat: This document includes regulatory mappings for developer reference and testing. Always validate configs against current AY changes (Finance Acts) and CBDT circular updates.


## Section 2d: TaxEngine — Line-by-Line Deep Analysis with Regulatory Notes

File: rede/engine/tax_engine.py

- L17–25 fy_label: Correct FY labeling (Apr–Mar) for India.
- L43–49 PreconstructionBucket: Encodes 1/5 deduction post-completion; aligns with Rule for pre-construction interest claim spread over five equal installments post-completion (see Section 24(b) Explanation; CBDT guidance).
- L61–76: Accumulators keyed by FY — enables correct FY settlement and credits carry-forward.
- L83–92 cfg_ihp/cfg_80c: Config-driven caps. Typical values: 24(b) self-occupied cap Rs 2,00,000; carry-forward of house property loss up to 8 AYs subject to set-off limitations (Section 71B). Ensure configs reflect the latest Finance Act.
- L93–100 cfg_mf["equity"]: LTCG exemption Rs 1,00,000; LTCG rate 10% without indexation; 365-day threshold — per Section 112A. Post-2023 debt MF taxation changed (many debt-oriented funds taxed as STCG); ensure config reflects current characterization and holding period rules.
- L75 rent_tds_credit_by_fy: Tracks credit for 194-IB (tenant deduction) — aligns with Form 26AS credit for the payee.

Suggested tests:
- Verify self-occupied interest cap application order when also claiming 1/5 pre-construction slices; ensure total interest deduction respects cap (Section 24(b)).
- Verify let-out NAV computation and 30% standard deduction is applied (Section 24(a)), separate from interest allowance.
- Verify MF CGT equity exemption application sequence and split between STCG/LTCG at 365 days.


## Section 2e: ExitEngine — Line-by-Line Notes with Regulatory Citations

File: rede/engine/exit_engine.py

- L44–50: Brokerage and transfer deducted from gross; buyer TDS at 194-IA computed on (price - brokerage - transfer) — typically, 194-IA applies on consideration; brokerage/transfer are not part of consideration paid to seller, so computing TDS on net-of-those aligns with deducting on seller’s receipt. CBDT has clarified GST exclusion but has limited direct prescriptions on brokerage treatment; conservative approach is TDS on the consideration to seller as per agreement value (without GST). Consider a config toggle.
- L81–93: CGT computation delegated to TaxEngine with acquisition basis resolved, including stamp/registration and improvements; indexation toggle handled in TaxEngine per Section 48 read with CII schedule; classification STCG/LTCG by holding period. Section 54/54F rollovers are toggles per spec — ensure TaxEngine respects when wired.
- Foreclosure penalty (L67–80): Treated as sale cost if paid on closure at sale time; while not explicitly listed in Section 48 as an expense of transfer, jurisprudence varies; keep as a configurable inclusion/exclusion for MTM and realized; annotate in report.

## Section 2f: LoanEngine — Notes with RBI References

- L50–57 compute_max_loan_cap: Uses LTV basis percent on agreement base (ex-GST) — aligns with bank practice. RBI’s broad LTV norms (RBI circulars on housing loans) impose tiered caps by ticket size; enforce via bank_policies config in future (gap).
- Rate resets and policy: AdjustTenure/AdjustEMI; repo-linked regimes per RBI circular (external benchmark lending rate) — implementation-wise, we’re policy-driven via inputs; document that index data can be fed from rate_index_path.

## Section 2g: Occupancy & Rent — Domain Notes

- Rent TDS Section 194-IB: Threshold Rs 50,000 per month; rate 5%; no TAN requirement for certain individuals/HUF; engine models net cash and TDS credit.
- IHP computation (TaxEngine): For let-out, NAV = Gross – vacancy – municipal taxes – 30% standard deduction (24(a)) – interest (24(b)). For self-occupied, NAV ~ 0, with interest cap per 24(b). Ensure occupancy gating via OC is respected (Timeline checks OC for rent start).

## Section 2h: Compliance & Safe Harbor (Income Tax Act Sections 50C/43CA/56(2)(x))

- MTM/realized sale: Consider adding safe-harbor checks where sale consideration below stamp duty value triggers deemed consideration (50C for capital assets, 43CA for stock-in-trade) and corresponding buyer taxation under 56(2)(x). Tolerance ranges (e.g., 10%) evolved across Finance Acts; keep in configs. Not yet implemented (gap), recommend integrating into ExitEngine/TaxEngine.


## Section 2i: TimelineEngine — Occupancy, Rent & TDS (Line References)

File: rede/engine/timeline.py (L318–420 excerpt)

- L321–334 Rent paid elsewhere (pre-OC): escalated by annual_rent_escalation_pct; no statutory impact beyond cashflow; aligns with realistic planning.
- L336–351 Rent saved after move-in: requires OC gate (oc_month not None and t ≥ oc_month), consistent with legal occupancy requirements.
- L352–369 Rent received (let-out) with OC gate: Gross = yield%/12 * V_t OR escalated fixed rent. Effective = gross * (1 – vacancy%) * collection% — corresponds to Income from House Property (IHP) gross less vacancy and collection inefficiency assumptions.
- L371–392 Letting brokerage and security deposit: Brokerage as cost; security deposit treated as inflow at start; stored for later refund — correct cash modeling; tax-neutrality depends on local practices; no direct IT Act section for refundable deposits.
- L393–417 PM Fees: Fixed/percent/both modes with narrative reasons; cost categorized as maintenance. For IHP, municipal taxes are deductible; PM fee is not part of 30% standard deduction separately — engine should ensure IHP calculation uses standard deduction rather than itemized O&M (TaxEngine side).
- L418– onward (not shown here): Rent TDS 194-IB computation: threshold of ₹50,000/month, TDS @5% for individuals/HUF (no TAN requirement) — Section 194-IB; ensure credit accumulation for payee.


## Section 2j: TimelineEngine — TDS Flow and KPI Exposure (Line References)

- L46–50: Introduces tds_credits accumulator; aligns with the notion that TDS is a tax credit (Form 26AS), not cash. Matches 194-IB treatment.
- L89–93: Loads 194-IB threshold and rate from tax config; ensure FY-specific values can differ if laws change.
- L418–429: If gross >= threshold, TDS = gross * rate; cash_in = effective - TDS; credit recorded via self.tds_credits and tax engine hooks (accrue_rent_tds_credit). Correct.
- L754 KPI: "tds_credits_accumulated" surfaced — useful for CA export and user-facing dashboards.

Regulatory references:
- Section 194-IB (TDS on rent by certain individuals/HUF) — threshold ₹50,000 per month; rate 5%; TAN not required.
- CBDT Notification/Guidance on 194-IB procedural aspects (Form 26QC/16C) for correct reporting — future CA export can include references.

## Section 2k: TimelineEngine — Waterfall, FY Settlement & Exit (Line References)

File: rede/engine/timeline.py (L440–768 completion)

- L440–452: Security deposit refund at tenancy end (tenancy_months); outflow with category "rent_received" — consistent with inflow treatment.
- L453–477: Waterfall sequence: Treasury.cover_shortfall first (accrual-only buckets), then FundingEngine (liquid → OD → MF → HL top-up). HL top-up risk flag added if used.
- L478–482: Month-end OD interest accrual and Treasury accruals (idle cash, coupons) — correct sequencing.
- L484–504: FY settlement logic: defer if horizon = March; force settlement at horizon (including MF CGT); normal March settlement otherwise. Ensures tax effects are captured.
- L506–530: Acquisition basis tracking per property; one-time buy-side brokerage on first acquisition month — correct for CGT cost basis.
- L531–561: Realized sale execution: deposit transfer/refund before sale proceeds; acquisition basis resolution; ExitEngine.realized_sale delegation.
- L562–594: XIRR calculations: realized (actual ledger flows) vs MTM (add hypothetical sale proceeds at horizon) — standard practice.
- L596–660: Narratives and risk flags generation: possession/OC milestones, peak OD/min liquid, MF usage, rate shocks, occupancy starts, risk thresholds (80% OD, 50% MF reliance, OC gate misses).
- L744–767: result() KPI mapping: comprehensive financial metrics, tax panel, risk flags, narratives, events, ledger — excellent observability.

Risk flag thresholds (L636–655):
- OD ≥80% of limit: liquidity stress indicator
- MF redemptions >50% of initial liquid: market risk elevation
- Let-out/move-in before OC: legal/operational risk
These are pragmatic heuristics; consider making thresholds configurable.
## Section 2l: TaxEngine — Function-by-Function Deep Analysis with Regulatory Citations

File: rede/engine/tax_engine.py (L100–250 excerpt)

- L100–107: MF CGT config defaults: equity 365-day threshold, 10% LTCG, ₹1L exemption, no indexation; debt 1095-day, 30% STCG, 20% LTCG, no exemption, no indexation — aligns with Section 112A (equity) and post-2023 debt MF taxation changes.
- L108–112: Property CGT config: 730-day threshold, 30% STCG, 20% LTCG — standard property CGT under Sections 111A/112.
- L119–136: _compute_owner_shares: distributes shareholdings per property; defaults to first person if no shares defined; normalizes if total ≠ 100% — pragmatic for multi-owner scenarios.
- L141–162: accrue_interest: separates pre-construction (bucketed for 1/5 rule) vs post-construction (self-occupied vs let-out) — correct per Section 24(b) Explanation and IHP classification.
- L163–170: accrue_principal_80c: allocates by ownership share per person — correct per Section 80C individual limits.
- L172–178: accrue_rent_effective: tracks gross effective rent per property/FY — feeds IHP NAV computation.
- L180–186: accrue_property_tax: municipal tax per property/FY — deductible under IHP (Section 24(a)).
- L188–193: accrue_rent_tds_credit: accumulates 194-IB credits by FY — correct for Form 26AS reconciliation.
- L195–199: set_preconstruction_start_on_oc: triggers 1/5 deduction start from OC FY — aligns with Section 24(b) Explanation.
- L207–220: settle_fy preconstruction 1/5 logic: slice = total/5; eligible if 0 ≤ (current_fy - start_fy) < 5 — correct implementation.
- L222–244: Section 24(b) computation: cap per person (₹2L default); equal split of self-occupied interest among persons — standard practice.

Regulatory alignment:
- Section 24(b): Interest on borrowed capital for house property; self-occupied cap ₹2,00,000; let-out unlimited subject to NAV.
- Section 80C: Principal repayment deduction up to ₹1,50,000 per person per FY.
- Section 112A: Equity LTCG 10% over ₹1,00,000 exemption; 365+ days holding.
- Post-2023 debt MF: Many debt-oriented funds taxed as STCG regardless of holding period — ensure config reflects current law.
## Section 2m: FundingEngine — Waterfall Logic and KPI Tracking (Line References)

File: rede/engine/funding_engine.py (L1–100 excerpt)

- L15–36: Class docstring: clear waterfall sequence (liquid → MF FIFO → OD → HL top-up); OD interest accrual/posting separation; TaxEngine MF redemption logging — excellent design clarity.
- L38–51: Initialization: pooled liquid from all persons; OD balance and interest carry-forward bucket — correct multi-person modeling.
- L54–57: KPI trackers: min_liquid/month, peak_od/month — essential liquidity stress indicators.
- L59–63: MF FIFO stack: sorted by acquired_date; redemptions_log for TaxEngine CGT — correct FIFO implementation for tax compliance.
- L72–86: KPI helpers: _note_min_liquid and _note_peak_od update trackers when new extremes reached — simple and effective.
- L92–100: begin_month: returns accrued OD interest from previous month for posting as current outflow; clears carry bucket — correct temporal separation for audit trails.

Design strengths:
- Clear separation of concerns: funding vs treasury vs tax
- Auditor-friendly OD interest posting (month t accrual → month t+1 outflow)
- FIFO MF redemption for tax compliance
- Comprehensive KPI tracking for risk assessment