#!/usr/bin/env bash
set -euo pipefail

# Usage:
#   ./scripts/run_all_fixtures.sh [FIXDIR]
# Env:
#   PYBIN=python3
#   RUNPY=scripts/run_fixture.py
#   PATTERN=*.json                   # glob filter within FIXDIR
#   LEDGER_TOP=10
#   JSON_OUT_DIR=artifacts           # save compact JSON results per fixture
#   FAIL_ON_RISKFLAGS=0              # set 1 to fail if any risk flags present

FIXDIR="${1:-tests/fixtures}"
PYBIN="${PYBIN:-python3}"
RUNPY="${RUNPY:-scripts/run_fixture.py}"
PATTERN="${PATTERN:-*.json}"
LEDGER_TOP="${LEDGER_TOP:-10}"
JSON_OUT_DIR="${JSON_OUT_DIR:-}"
FAIL_ON_RISKFLAGS="${FAIL_ON_RISKFLAGS:-0}"

# Optional tools
if command -v jq >/dev/null 2>&1; then HAS_JQ=1; else HAS_JQ=0; fi

if [[ ! -f "$RUNPY" ]]; then
  echo "ERROR: $RUNPY not found. Run from repo root or set RUNPY env." >&2
  exit 1
fi
if [[ ! -d "$FIXDIR" ]]; then
  echo "ERROR: Fixture directory '$FIXDIR' not found." >&2
  exit 1
fi

# Pretty
BOLD=$(printf '\033[1m'); DIM=$(printf '\033[2m'); GREEN=$(printf '\033[32m'); YELLOW=$(printf '\033[33m'); RED=$(printf '\033[31m'); NC=$(printf '\033[0m')

printf "${BOLD}Running fixtures in %s${NC}\n" "$FIXDIR"
printf "${DIM}Using %s | jq=%s | top-ledger=%s | pattern=%s${NC}\n\n" "$PYBIN" "$HAS_JQ" "$LEDGER_TOP" "$PATTERN"

printf "${BOLD}%-34s | %-12s | %-12s | %-10s | %-10s | %-10s | %-10s | %-3s${NC}\n" "Fixture" "Outflows(₹)" "Inflows(₹)" "Net(₹)" "RealXIRR" "MTMXIRR" "MinCash(₹)" "RF"
printf -- "---------------------------------------------------------------------------------------------------------------------------------------\n"

fail=0
warn=0
total=0

shopt -s nullglob
for f in "$FIXDIR"/$PATTERN; do
  total=$((total+1))
  # Capture exit status too
  if ! out=$("$PYBIN" "$RUNPY" "$f" --json --top-ledger "$LEDGER_TOP" 2>/tmp/run_fixture_err.log); then
    echo "ERROR running $f"
    cat /tmp/run_fixture_err.log >&2 || true
    fail=$((fail+1))
    continue
  fi

  if [[ -n "${JSON_OUT_DIR}" ]]; then
    mkdir -p "$JSON_OUT_DIR"
    echo "$out" > "${JSON_OUT_DIR}/$(basename "$f" .json).result.json"
  fi

  if [[ "$HAS_JQ" -eq 1 ]]; then
    k_out=$(echo "$out" | jq -r '.kpis.total_outflows // 0')
    k_in=$(echo "$out" | jq -r '.kpis.total_inflows // 0')
    k_net=$(echo "$out" | jq -r '.kpis.net_cash // 0')
    k_rxirr=$(echo "$out" | jq -r '.kpis.realized_xirr // "—"')
    k_mtmxirr=$(echo "$out" | jq -r '.kpis.mtm_xirr // "—"')
    k_min=$(echo "$out" | jq -r '.kpis.min_liquid_balance // 0')
    rf_count=$(echo "$out" | jq -r '.risk_flags | length')

    color="$NC"
    if [[ "$rf_count" -gt 0 ]]; then color="$YELLOW"; warn=$((warn+1)); fi
    if awk "BEGIN{exit !($k_net < 0)}"; then color="$RED"; fi

    printf "%-34s | %12.0f | %12.0f | %10.0f | %10s | %10s | %10.0f | %3d\n" \
      "$(basename "$f")" "$k_out" "$k_in" "$k_net" "$k_rxirr" "$k_mtmxirr" "$k_min" "$rf_count" | sed "s/^/$color/;s/$/$NC/"

    if [[ "$FAIL_ON_RISKFLAGS" -eq 1 && "$rf_count" -gt 0 ]]; then
      fail=$((fail+1))
    fi
  else
    printf "%-34s | %s\n" "$(basename "$f")" "jq not found — showing text summary below:"
    "$PYBIN" "$RUNPY" "$f" -n "$LEDGER_TOP" || fail=$((fail+1))
    echo
  fi
done

printf "\n${BOLD}Done.${NC} Processed %d fixture(s). " "$total"
if [[ "$fail" -gt 0 ]]; then
  printf "${RED}%d failed.${NC} " "$fail"
fi
if [[ "$warn" -gt 0 ]]; then
  printf "${YELLOW}%d had risk flags.${NC} " "$warn"
fi
printf "\n"

exit "$fail"
