from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger,
    PropertyOwnershipCosts, PropertyOccupancy, ExitSettings
)
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD

def _ctx_tax():
    class _Cfg:
        content = {
            "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
            "cii": {"FY2024-25": 348, "FY2025-26": 360, "FY2026-27": 372}
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def _run_with_sale(sale_month: int):
    ctx = _ctx_tax()
    buy = PaymentEvent(
        name="Purchase", stage="SaleDeed",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
        percent_of_base=DD(100), gst_pct=DD(0), financeable_by_bank=False
    )
    prop = Property(
        id="P1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(3_000_000)),
        plan=PaymentPlan(events=[buy]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        exit=ExitSettings(sale_month=sale_month, brokerage_pct=DD(1.0), buyer_tds_pct=DD(1.0))
    )
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(5_000_000))
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=max(sale_month+2, 40),
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(5_000_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )
    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf
    eng.run()
    return eng.result()

def _sum_property_cgt(ledger):
    return sum(float(l["amount"]) for l in ledger if "Capital Gains" in l["label"])

def test_stcg_vs_ltcg_tax_difference():
    res_st = _run_with_sale(20)  # STCG window (< 24 months)
    res_lt = _run_with_sale(37)  # LTCG window (>= 36 months)
    tax_st = _sum_property_cgt(res_st["ledger"])
    tax_lt = _sum_property_cgt(res_lt["ledger"])
    # With same base and similar appreciation path, LTCG tax should not exceed STCG tax:
    assert tax_lt <= tax_st
