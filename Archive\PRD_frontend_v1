PRD — Real Estate Decision Engine (Front-End & UI/UX)

Version: 1.0 (Frontend PRD)
Audience: Product, Design, Frontend & Full-stack Developers
Goal: Ship a modern, trustworthy, production-quality UI that runs the existing backend engine to help Indian buyers/investors evaluate property scenarios with clarity, speed, and rigor.

1) Product Summary
1.1 What this app does

Lets users create scenarios for purchasing/holding/selling properties (RTM/UC/resale/portfolio), simulate month-by-month cash flows, taxes, funding waterfall, exits & MTM, then returns KPIs, risk flags, narratives, and a full ledger.

Two UX modes:

Simple Mode (Consumer) — 3–5 steps to answer: “Am I okay? How much buffer? Rent vs Buy now?”

Detailed Mode (Advisor/Investor) — exposes all toggles (loans, rate shocks, UC stages, taxes, Sec 54/54F, NRI TDS, etc.)

1.2 Target personas

Primary: First-time buyer, mid/upper-income salaried, confused about affordability and timing.

Secondary: Investor/advisor analyzing portfolios with multiple assets, rent flows, taxes, and exits.

Tertiary: Power users (CAs, RMs, RE advisors) who need defensible analytics for clients.

1.3 Success criteria (v1)

A user can:

Build a scenario in Simple or Detailed Mode

Run it and see KPIs, liquidity/waterfall, tax & ledger

Save, clone, tweak, and compare up to 3 scenarios

Export summary PDF & ledger CSV

Perceived performance: results returned within ~3–8s on typical scenarios.

Trust signals: clear narratives & risk flags; no unexplained numbers.

2) Tech & Architecture (Frontend)

Framework: Next.js (App Router) + TypeScript

Styling: Tailwind CSS + shadcn/ui (Radix)

Charts: Recharts (Line/Area/Bar; no custom color required unless specified)

State & Data: React Query for server calls; zod/react-hook-form for forms

Validation Schema: generated from backend Portfolio JSON Schema (see §9)

Storage: localStorage for autosave drafts; optional backend persistence later

i18n: English (Phase-1), INR with Indian numbering format

A11y: WCAG 2.1 AA (keyboard support, labels, focus rings)

Analytics: simple event layer (see §13)

Security: HTTPS, no secrets stored client-side (see §12)

Folder layout (suggested):

app/
  layout.tsx
  page.tsx                       # Landing: choose Simple/Detailed
  scenarios/
    new/page.tsx                 # Wizard entry
    simple/page.tsx
    detailed/page.tsx
    [id]/page.tsx                # Scenario detail/results
    [id]/compare/page.tsx        # Scenario comparison
  fixtures/page.tsx              # Dev/QA: run fixtures from UI

components/
  forms/                         # All form editors
  panels/                        # KPI/Risk/Waterfall/Tax/Timeline views
  charts/                        # Small chart wrappers
  common/                        # Buttons, tooltips, money formatter

lib/
  api.ts                         # Typed API client
  schema/portfolio.schema.json   # From backend/validator script
  types.ts                       # TS types from schema
  format.ts                      # Money/date formatting
  defaults.ts                    # Smart defaults logic

styles/                          # Tailwind config, tokens


3) Information Architecture & Navigation
3.1 Top-level

Home: Choose Simple vs Detailed; CTA “Create Scenario”

Scenarios:

New (Simple/Detailed)

Saved (list; search by name/date; clone; delete)

Compare (select up to 3)

Fixtures (Dev/QA): Run predefined JSON fixtures, see outputs

Docs/Help: Glossary & microcopy; explainer videos (optional)

3.2 Key Views

Simple Wizard (3–5 steps): Basics → Occupancy → Costs → Risk/Buffer → Review & Run

Detailed Editor (Tabbed): Property → Payment Plan → Financing → Occupancy & Rent → Taxes → Shocks & Sensitivities → Review & Run

Results:

KPI Header

Funding Waterfall + Liquidity line

Risk Flags panel

Narrative bullets

Tax panel (IHP/MF CGT/Property CGT)

Ledger (filterable & exportable)

Compare:

Side-by-side KPIs

Radar chart (Return / Liquidity / Risk / Tax)

Cash line sparklines per scenario

4) Detailed User Journeys
4.1 Simple Mode — Create & Run

Basics

City (select) → sets smart defaults

Property price (BSP), Down payment, Loan amount/tenure (optional)

Toggle “I already own this property” → moves user to onboarding flow (existing basis)

Validation: BSP > 0; down payment ≥ 0; if loan set → tenure 1–360m

Occupancy

Move-in at possession vs Let-out vs Stay on rent till OC

Rent while waiting (default by city; editable)

Vacancy (default 5%)

Costs

Maintenance (city default; editable), escalation default 5% pa

Interiors (toggle): amount + depreciation model (default: declining 12–15%)

Risk/Buffer

Desired start buffer (₹), interest rate sensitivity slider (±1.5%), price path (Base/Bullish/Correction@Y3)

Review & Run

Summary panel with all assumptions + “Edit” links

CTA: Calculate

Results

KPIs, Waterfall + cash line, Risk flags, Narratives, Tax panel (collapsed), Ledger (collapsed)

Actions: Save, Clone, Export PDF, Export CSV, Compare

Acceptance Criteria (AC)

AC-S1: User can complete wizard with only required fields and get results without console errors.

AC-S2: City changes recalc smart defaults (yields, maintenance) immediately.

AC-S3: Results include at least 6 KPIs (Net Value, Realized/MTM XIRR, Total Outflows/Inflows, Min Cash, Months Shortfall).

AC-S4: Export PDF includes KPIs, charts, risk flags, narratives, assumption summary.

4.2 Detailed Mode — Create & Run

Tabs with autosave; right pane shows mini preview (last run KPIs & cash sparkline).

Users can import payment plan via CSV paste (stage, % of base, month, financeable).

Can define MF holdings, OD limit, planned top-ups, rate shocks, Sec 54/54F, NRI TDS, etc.

AC

AC-D1: CSV paste populates stage table with validation (“Sum exceeds 100%? Autonormalize?”).

AC-D2: Loan editor supports Term vs Overdraft; floating policies; MRTA (upfront or annual).

AC-D3: Funding strategy supports FIFO MF, OD, HL top-up toggle, planned top-ups schedule.

AC-D4: Taxes tab supports Old/New regime, IHP caps, 194-IB, NRI 195, Sec 54/54F toggles.

AC-D5: Shocks tab supports price path presets + custom events (rate changes; builder delay).

AC-D6: Calculate returns full result without unhandled errors.

4.3 Results View

KPI Header: Net Value (MTM), Realized XIRR (if any exits), MTM XIRR, Total Outflows/Inflows, Min Cash, Months with Shortfall, Peak OD used, MF redeemed, HL-top-ups used.

Funding Waterfall: Monthly stacked bars [Liquid, MF, OD, HL Top-up], hover tooltips with ₹ and reason.

Timeline Chart: Cash line; markers for big events (demands, exits, taxes).

Risk Flags: Pills (info/warn/risk). Clicking a flag filters timeline to relevant months and shows mitigation tips.

Narratives: Bulleted insights (“Biggest crunch is M33… add ₹5L top-up at M30 to avoid OD”).

Tax Panel: FY chips; click to open IHP/MF CGT/Property CGT tables.

Ledger Table: Virtualized; filter by property/person/category/date; export CSV.

Actions: Save scenario (name/tags), Clone, Compare, Export (PDF/CSV/JSON).

AC

AC-R1: Hover on any bar gives exact amounts and attribution (e.g., “Funding MF: ₹2,10,000 (FIFO: EQ-OLD)”).

AC-R2: Clicking a risk flag scrolls to first relevant month/event and highlights it.

AC-R3: Ledger export CSV preserves at least these columns: date, month_index, property_id, person, category, label, amount (signed), running_liquid, reason code.

4.4 Compare

Select up to 3 saved scenarios → side-by-side KPIs, radar chart, sparklines, risk badges.

Choose a primary metric to highlight best scenario (e.g., “Highest MTM XIRR”).

AC-C1: Selecting primary metric updates highlight instantly; no full rerun required.

4.5 Fixtures (Dev/QA Page)

Upload/select any JSON fixture from tests/fixtures/ UI (drag-drop or file picker).

Run and display results as with normal scenarios; optionally show diff to schema.

AC-F1: Errors from backend are displayed as readable toasts and inline red banners with helpful next steps.

5) Component Specs (Key)
5.1 KPIHeader

Props: kpis: Kpis, lastRunAt: string, currency: "INR"

Kpis fields (minimum):

net_value_mtm, realized_xirr, mtm_xirr, total_outflows, total_inflows, min_liquid_balance, months_with_shortfall, peak_od_utilization_pct, mf_redeemed_amount, hl_topup_amount_used

Behavior: Currency formatted with Indian grouping; green for positive, red for negative; tooltip on each with definition.

AC: 0/NaN/Null handled gracefully with “—”.

5.2 FundingWaterfall

Props: series: Array<{monthLabel: string; liquid: number; mf: number; od: number; hl: number}>

Chart: Stacked bar (Recharts); legend; hover value details.

AC: stacking sums to net monthly funding; bars align with timeline months.

5.3 TimelineChart

Props: cashSeries, eventMarkers (array: {month, label, severity})

AC: Hover shows cash and events; clicking event scrolls ledger to that month.

5.4 RiskFlagsPanel

Props: flags: string[]

Behavior: Pill badges with severity color; click opens drawer with details & mitigation.

Strings (examples):

“OD utilisation reached ≥80% in months 11–12. Mitigation: planned top-up ₹3.8L @ M10.”

“Relied on HL Top-up of ₹12L in M24. Bank approval not guaranteed—verify eligibility.”

5.5 NarrativePanel

Props: items: string[]

AC: Multi-line bullets; copy-to-clipboard button.

5.6 LedgerTable

Props: rows, filters, onExportCsv

Columns: date, month, property, person, category, label, amount (₹), running_liquid, reason

Behavior: column filters; virtualization for large ledgers; sticky header.

AC: Export CSV respects current filters.

5.7 Editors (Simple/Detailed forms)

Built with react-hook-form + zod (inferred from schema).

City defaults display as inline “Suggested” badges; user overrides persist.

All numeric inputs support “₹ lakhs / crores” entry helper (e.g., typing “1.2cr” → 1,20,00,000).

6) Visual Design & Theming (Modern fintech aesthetic)
6.1 Tokens (Tailwind config or CSS variables)

Colors:

Primary: #2E5BFF

Success: #1AAE70

Warning: #F9A826

Danger: #E5484D

Neutral base: #0F172A (text), #F8FAFC (bg), cards #FFFFFF, borders #E2E8F0

Radii: Card/Button: 1rem (rounded-2xl)

Shadows: md for cards; lg on hover for primary CTA

Typography: Inter or SF; 14/16/20/28 px scales; H1=28, H2=20, body=14/16

Spacing: 8-pt grid; cards ≥ 16px padding

Dark Mode: optional later; ensure colors have contrasts ≥ 4.5:1

6.2 Layout

App max width: 1200px; tool panels two-column on desktop; single column on mobile.

Skeleton loaders for charts & tables (pulse shimmer).

7) Smart Defaults Logic (Front-end side)

Fallback logic (executed when backend config endpoints aren’t available):

Rent yield:

Luxury Mumbai 2.3–2.8% (default 2.5%)

BLR/Pune 2.5–3.2% (default 2.8%)

Maintenance: ₹2.5–₹5/sqft; if sqft unknown, default ₹4,000–₹6,000/month (adjustable)

Vacancy: 5% (default)

Escalation: rent 5% pa; maintenance 5% pa

Interiors depreciation: declining 12–15% pa (default 12%)

IHP caps/194-IB thresholds: from backend tax config if available; else show tooltips noting assumptions

Rate sensitivity slider: ±1.5% around initial ROI

All defaults are overrides by user and explicitly shown (with a “Suggested” chip) to build trust.

8) Forms & Validation Rules (non-exhaustive)

BSP: required, >0

Down payment: ≥ 0; if loan present: down + loan ≤ total acquisition (soft warn if exceed)

Loan tenure: 1–360 months

Events (Detailed): % of base sum ≤ 100%; if >100% offer Auto-normalize

Occupancy move-in: blocked till OC received (if OC gating on)

NRI rent: when person.residency == "NRI", show 195 TDS info tooltip

Sec 54/54F toggle: show note: rollover window constraints; warn if no eligible reinvestment within 24/36 months.

Errors are inline, with helper text and “Learn more” links to glossary.

9) Backend Integration & API Contracts

The frontend sends one complete Portfolio JSON and receives calculation results.

9.1 Required Endpoint

POST /api/calculate_scenario

Request body: Portfolio JSON (as per backend models).

Use the JSON Schema exported by:

python rede/scripts/check_fixtures_schema.py --print-jsonschema > app/lib/schema/portfolio.schema.json


Response 200:

{
  "kpis": {
    "net_value_mtm": 0,
    "realized_xirr": 0.0,
    "mtm_xirr": 0.0,
    "total_outflows": 0,
    "total_inflows": 0,
    "min_liquid_balance": 0,
    "months_with_shortfall": 0,
    "peak_od_utilization_pct": 0,
    "mf_redeemed_amount": 0,
    "hl_topup_amount_used": 0
  },
  "risk_flags": ["OD utilisation reached ≥80% in months 11–12"],
  "narrative_summary": ["Your largest cash crunch occurs in Month 33…"],
  "ledger": [
    {
      "date": "2025-05-05",
      "month_index": 1,
      "property_id": "UC1",
      "person": "u",
      "category": "funding_mf",
      "label": "Funding via MF (FIFO: EQ-OLD)",
      "amount": -210000,
      "running_liquid": 389000,
      "reason": "SHORTFALL"
    }
  ],
  "tax_panel": {
    "ihp": { /* FY breakdowns */ },
    "mf_cgt": { /* lot-wise */ },
    "property_cgt": { /* sale-wise */ }
  },
  "price_panel": { /* MTM snapshots if present */ }
}


Errors 4xx/5xx:

{ "error": "ValidationError", "message": "Loan tenure must be > 0", "details": { "path": "loan.tenure_months" } }

9.2 Optional Endpoints (nice-to-have, can be mocked)

GET /api/configs → returns city/state/tax defaults for smart suggestions

GET /api/schema/portfolio → serves the Portfolio JSON Schema (else we bundle a copy at build time)

POST /api/validate_scenario → validates without running engine (rarely needed)

POST /api/export/pdf → takes result payload and returns a branded PDF (frontend can also print-to-PDF)

9.3 Frontend API Client (lib/api.ts)

calculateScenario(portfolio: Portfolio): Promise<CalcResult>

getConfigs(): Promise<Configs> (fallback to local defaults on failure)

Network behavior

Show spinner while calculating; time-out after 60s with retry CTA.

On 4xx/5xx, show toast and inline error banner; do not drop the user’s input.

10) Performance, Reliability & Security

React Query: dedupe requests, cache last successful result

Debouncing: Don’t auto-recalculate on every keystroke; explicit “Calculate” CTA

Large ledgers: use windowed list (react-virtualized/react-window)

CSV export: stream or chunk to avoid blocking UI

Security: HTTPS only; sanitize CSV content; do not store PII beyond what’s needed; avoid embedding secrets.

Error reporting: capture UI unhandled errors (Sentry or similar)

11) Accessibility (WCAG 2.1 AA)

Keyboard reachable: all inputs, tabs, table filters

ARIA roles/labels for charts (summary text below charts)

Focus visible; no content shift on validation errors

Sufficient color contrast (check tokens across light/dark backgrounds)

12) Privacy

No login (v1); drafts saved in localStorage only

If adding auth later, store scenarios server-side under user account; ensure data encryption at rest; delete on request.

13) Analytics Events

Minimal, privacy-safe event set:

scenario_created (mode: simple|detailed)

scenario_calculated (duration_ms, errors=0/1)

scenario_saved (id)

scenario_compared (ids_count)

export_pdf / export_csv

risk_flag_viewed (flag_type)

fixture_run (dev only)

All events contain no PII or financial raw values; only metadata.

14) QA Plan (Frontend)

Unit tests for helpers (formatting, defaults)

Integration tests for forms (Simple/Detailed), validation, and API error handling

Visual regression for KPI header & charts on common breakpoints

E2E tests (Playwright) for full journeys:

Simple Mode → Calculate → Export

Detailed Mode with CSV import → Calculate → Risk flag → Ledger filter → Export CSV

Compare view with 3 scenarios

15) Microcopy & Glossary (selected)

Rent Saved: Money you no longer pay as rent after moving into your new home. Modeled as a positive inflow.

Funding Waterfall: The order in which shortfalls are funded: Liquid → MF (FIFO) → OD → HL Top-up.

Realized XIRR: Your actual annualized return based only on cash you’ve paid/received from start to sale.

MTM XIRR: Your annualized return if you liquidate at today’s market value (includes paper gains/losses).

Risk Flag: A warning that highlights a pressure point (e.g., high OD usage) and how to mitigate it.

16) Implementation Milestones (Frontend)

M1 — Simple Mode + Results (2–3 weeks)

Simple wizard + calculate + basic Results (KPIs, Waterfall, Narratives)

Load fixture page (for QA)

Export CSV/PDF (basic)

M2 — Detailed Mode (3–4 weeks)

Tabbed editor with CSV import for stages

Loan & funding editors

Tax panel & Ledger (filterable)

M3 — Compare & Polish (2 weeks)

Compare view

Risk flag deep links, better narratives

Design polish & performance tuning

17) TypeScript Types & Example Payloads
17.1 Portfolio Type (frontend)

Generate via:

python rede/scripts/check_fixtures_schema.py --print-jsonschema > app/lib/schema/portfolio.schema.json


Then convert to zod/TS types (tooling: json-schema-to-typescript or zod-to-ts), and wire forms accordingly.

17.2 Example calculateScenario usage (pseudo-TS)
import { calculateScenario } from "@/lib/api";
import type { Portfolio } from "@/lib/types";

const portfolio: Portfolio = {/* ...from form */};

const res = await calculateScenario(portfolio);
/*
res.kpis, res.risk_flags, res.narrative_summary, res.ledger, res.tax_panel, res.price_panel
*/

18) Design System Checklists (per screen)

Wizard Step (Simple):

Labels + helper text for each field

“Suggested default” chips visible next to fields

Next/Back buttons (sticky footer on mobile)

“Calculate” disabled until required fields valid

Detailed Tabs:

Unsaved changes prompt on navigation

CSV paste has preview + confirm dialog

Error summary panel at top

Results:

KPI header responsive (wraps to 2 rows on mobile)

Charts with fallback text if no data

Ledger performance tested on 10k rows

Compare:

Side-by-side on desktop; stacked on mobile

Primary metric dropdown persists per user session

19) Visual Wireframe Notes (textual)

KPI Header (top): Grid of cards (3 columns desktop, 2 tablet, 1 mobile), value + small label + tooltip.

Waterfall + Timeline (middle): Two cards side by side; on mobile stacked; shared x-axis months.

Risk Flags & Narratives (bottom left): flags as pills; narratives as numbered list.

Tax & Ledger (bottom right): accordions collapsed by default; expanding opens tables and charts.

Actions Bar (sticky bottom on mobile): Save · Clone · Compare · Export.

20) Dev Notes for Builders

Do not hardcode defaults; read from /api/configs if available; else use lib/defaults.ts.

Keep /api/calculate_scenario pure; no partial updates.

Use react-query useMutation for calculate; optimistic UI not required.

Recharts: one chart per panel; no explicit color theme unless asked; let default theme + tokens drive.

Indian currency formatting everywhere (₹ 1,23,45,678), two decimals where necessary (EMI/monthly), else zero decimals for large KPIs.

Appendix A — Error States (UX)

ValidationError (4xx): show inline field highlight + red banner “We couldn’t run due to input errors: Loan tenure must be > 0.”

EngineError (5xx): toast “Our engine had a problem. Please try again in a minute.” + link to contact.

Timeout: “This is taking longer than expected. You can wait or adjust inputs to simplify the calculation.”

Appendix B — Export Specs

PDF: Title + date/time + scenario name; KPIs; waterfall snapshot; cash line; top 6 narratives; risk flags; assumptions table (city, BSP, loan, occupancy, taxes).

CSV: ledger with all columns; UTF-8; header row; comma-delimited.

Appendix C — Future Enhancements (not blocking v1)

Auth + server-side saved scenarios

Dark mode

Multi-language (EN/HI)

Advisor workspace: client profiles, shareable links

What-if sliders overlaying results