import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.types import D as DD
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy, ExitSettings
)

def _ctx():
    class _Cfg:
        content = {
            "cii": {"FY2024-25": 348, "FY2025-26": 360, "FY2026-27": 372},
            # Model NRI rent TDS under Sec 195 (illustrative)
            "tds_rules": {
                "rent_nri_195": {"monthly_rate_pct": 31.2},   # e.g., 30% + SC + Cess combined
                "rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}  # resident baseline
            },
            "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
            # NRI sale TDS by buyer (illustrative config knob if supported)
            "nri_sale_tds": {"ltcg_tds_pct": 20, "stcg_tds_pct": 30}
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_s11_nri_rent_tds_and_sale_tds_if_supported():
    """
    Scenario: NRI owns RTM flat; rent 60k/m; sell after 5y (LTCG).
    Expect:
      - Rent TDS at NRI rate (31.2) posted (if NRI toggles exist)
      - Buyer TDS on sale for NRI (e.g. 20%) posted
    """
    ctx = _ctx()

    # Check if Person has residency or nri flag
    sample = Person(id="nri", name="NRI Owner", tax_regime="old", shareholdings={}, liquid_cash=DD(500_000))
    if not (hasattr(sample, "residency") or hasattr(sample, "is_nri")):
        pytest.skip("NRI residency flag not available in Person model")

    person = sample
    # Flip whichever attribute exists
    if hasattr(person, "residency"):
        try:
            person.residency = "NRI"
        except Exception:
            pytest.skip("Person.residency present but not assignable")
    elif hasattr(person, "is_nri"):
        person.is_nri = True

    prop = Property(
        id="NRI1", city="Mumbai",
        heads=PropertyHeads(base_price_ex_gst=DD(20_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(annual_property_tax=DD(25000)),
        occupancy=PropertyOccupancy(move_in_month=None, let_out_month=0, rent_paid_while_waiting=None,
                                    rent_yield_pct_of_value=DD(3.6)),  # ~60k/m on 2Cr
        exit=ExitSettings(sale_month=60, brokerage_pct=DD(1.0), buyer_tds_pct=DD(0))  # buyer_tds_pct used only for resident path; NRI path may override
    )

    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=72,
        persons=[person], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"nri": DD(500_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
    res = eng.result()
    ledger = res["ledger"]

    # Rent TDS lines should reflect NRI rule if wired
    rent_lines = [l for l in ledger if l.get("category") == "rent_received"]
    if not rent_lines:
        pytest.skip("No rent lines; occupancy/rent not posted in this build")
    # TDS postings for NRI rent: look for 195 or 'NRI Rent TDS' labels
    tds_rent = [l for l in ledger if "Rent TDS" in l.get("label","") or "195" in l.get("label","")]
    if not tds_rent:
        pytest.skip("NRI rent TDS (195) not wired; skipping")

    # On sale, NRI buyer TDS (separate from resident 194-IA) should appear if wired
    sale_tds = [l for l in ledger if "Buyer TDS (NRI)" in l.get("label","") or "NRI Sale TDS" in l.get("label","")]
    if not sale_tds:
        pytest.skip("NRI sale TDS not wired; skipping")

    # If present, both mechanisms worked
    assert tds_rent and sale_tds
