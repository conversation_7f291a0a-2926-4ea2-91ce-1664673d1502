from pydantic import BaseModel, Field, root_validator
from ..types import Money, Percent, D

class PriceHeads(BaseModel):
    base_price_ex_gst: Money = Field(default=D(0))
    plc_floor_rise: Money = Field(default=D(0))
    parking_charges: Money = Field(default=D(0))
    club_membership: Money = Field(default=D(0))
    other_capital_heads: Money = Field(default=D(0))

    gst_pct: Percent = Field(default=D(0), ge=D(0), le=D(28))
    stamp_duty_pct: Percent = Field(default=D(6.6), ge=D(0), le=D(10))
    registration_fixed: Money | None = None
    registration_pct: Percent | None = None

    brokerage_buy_pct: Percent = Field(default=D(0))
    tds_buy_pct: Percent = Field(default=D(1.0))

    khata_mutation: Money = Field(default=D(0))
    utility_connection: Money = Field(default=D(0))
    corpus_or_advance_maint: Money = Field(default=D(0))
    fitout_deposit_refundable: Money = Field(default=D(0))
    fitout_nonrefundable: Money = Field(default=D(0))

    interiors_capex: Money = Field(default=D(0))
    interiors_depr_years: int = Field(default=10, ge=1, le=40)
    interiors_residual_pct: Percent = Field(default=D(20), ge=D(0), le=D(100))


    @root_validator
    def _registration_exclusive(cls, v):
        fx, pct = v.get('registration_fixed'), v.get('registration_pct')
        if fx and pct:
            raise ValueError('Use either registration_fixed or registration_pct, not both.')
        return v
