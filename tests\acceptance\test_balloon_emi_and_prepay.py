import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Port<PERSON>lio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.types import D as DD

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_balloon_emi_and_prepayment_if_supported():
    """
    Scenario:
      - Loan EMI is balloon type (smaller EMIs + big final payment).
      - At m=24, borrower prepays 20% principal.
    Expect:
      - Ledger shows EMI postings with varying amounts
      - Prepayment event reduces outstanding and subsequent interest
    """
    ctx = _ctx()

    if not hasattr(FundingStrategy, "loan_type"):
        pytest.skip("Loan type feature not available")

    prop = Property(
        id="BLN1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(6_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    person = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(1_000_000))

    fs = FundingStrategy(
        loan_amount=DD(3_000_000), loan_annual_rate_pct=DD(9), loan_tenure_months=60,
        loan_type="balloon",  # must be supported
        od_limit=DD(0), mf_fifo_enabled=False
    )

    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=60,
        persons=[person], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(1_000_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=fs,
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    # Inject prepayment schedule if possible
    if not hasattr(pf, "loan_prepayments"):
        pytest.skip("Prepayment schedule not supported")
    try:
        pf.loan_prepayments = [{"month": 24, "percent_principal": 20}]
    except Exception:
        pytest.skip("loan_prepayments field present but not assignable")

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
    ledger = eng.result()["ledger"]

    emis = [l for l in ledger if "emi" in l.get("category","").lower()]
    if not emis:
        pytest.skip("No EMI postings")
    assert len(emis) >= 2
    prepay = [l for l in ledger if "prepay" in l.get("category","").lower()]
    assert prepay, "Expected prepayment posting"
