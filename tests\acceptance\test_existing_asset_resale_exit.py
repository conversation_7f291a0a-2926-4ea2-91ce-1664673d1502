from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy, ExitSettings
from rede.types import D as DD

def _ctx_tax():
    class _Cfg:
        content = {
            "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
            "cii": {"FY2024-25": 348}
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_existing_asset_resale_uses_onboarded_cost_basis():
    ctx = _ctx_tax()
    prop = Property(
        id="R1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(0)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        exit=ExitSettings(sale_month=1, brokerage_pct=DD(1.0), buyer_tds_pct=DD(1.0))
    )
    # Seed onboarded acquisition cost (hydrated existing asset path)
    # The Exit/Tax engine should use this when builder events are absent.
    prop._acq_total_cached = DD(1_100_000)

    p = Person(id="u", name="Owner", tax_regime="old", shareholdings={"R1": 100}, liquid_cash=DD(50_000))
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=6,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(50_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )
    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf
    eng.run()
    led = eng.result()["ledger"]
    # Expect sale and CGT lines present; the exact rupee values depend on price engine,
    # so we assert presence and structure only.
    assert any("Sale Proceeds (net of buyer TDS)" in l["label"] for l in led)
    assert any("Capital Gains" in l["label"] for l in led)
