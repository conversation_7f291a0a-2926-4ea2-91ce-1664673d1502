import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.types import D as DD

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_price_shock_stepjump_affects_mtm_if_supported():
    """
    Goal: If a price path shock model is available, apply a step jump at m=12
    and check that MTM net value after m=12 > before.
    """
    ctx = _ctx()

    prop = Property(
        id="SHK1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(5_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(0))

    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=24,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(0)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    # Try injecting a price_shocks attribute if supported
    if not hasattr(prop, "price_shocks"):
        pytest.skip("Property.price_shocks not available (skipping)")

    try:
        prop.price_shocks = [{"month": 12, "jump_pct": 20}]  # +20% at m=12
    except Exception:
        pytest.skip("price_shocks field present but not assignable")

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
    ledger = eng.result()["ledger"]

    # If price shocks wired, we expect MTM valuation jump at m=12
    vals = [l for l in ledger if l.get("category") == "valuation"]
    if not vals:
        pytest.skip("No valuation postings; skipping check")
    before = [v for v in vals if v["month_index"] < 12]
    after = [v for v in vals if v["month_index"] >= 12]
    if not before or not after:
        pytest.skip("Not enough valuation points")
    assert max(v["amount"] for v in after) > max(v["amount"] for v in before)
