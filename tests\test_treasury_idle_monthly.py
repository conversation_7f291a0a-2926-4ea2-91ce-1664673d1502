from decimal import Decimal
from rede.engine.context import EngineContext
from rede.engine.timeline import TimelineEngine
from rede.models.core import Portfolio, FundingStrategy, TaxSettingsGlobal, RuntimeConfigs, Person
from rede.models.treasury import TreasuryConfig
from rede.types import D

def _ctx(portfolio: Portfolio):
    class DummyCfg: content = {}
    class DummyCtx(EngineContext):
        def __init__(self, p): 
            self.portfolio = p
            self.tax_laws_fy = DummyCfg()
    return DummyCtx(portfolio)

def test_idle_compounds_monthly():
    p = Portfolio(
        currency="INR",
        start_date="2025-04-05",
        posting_day_of_month=5,
        horizon_months=12,
        persons=[Person(id="u1", name="User", tax_regime="old")],
        properties=[],
        mf_holdings=[],
        user_starting_liquid_cash_by_person={"u1": D(1000000)},
        treasury_config=TreasuryConfig(idle_return_after_tax_pct=D(12)),
        funding_strategy=FundingStrategy(),
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs(state_rules_path="", city_benchmarks_path="", tax_laws_path="", rate_index_path="", bank_policies_path="")
    )
    ctx = _ctx(p)
    eng = TimelineEngine(ctx)
    eng.run()
    assert eng.funding.liquid_bal > D(1000000)  # grew
