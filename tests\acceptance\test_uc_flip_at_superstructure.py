from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger,
    PropertyOwnershipCosts, PropertyOccupancy, ExitSettings
)
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD

def _ctx_basic_tax():
    class _Cfg:
        content = {
            "tds_rules": {"rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}},
            "mf_tax": {"equity": {"holding_days_ltcg": 365, "stcg_rate_pct": 15, "ltcg_rate_pct": 10,
                                  "ltcg_annual_exemption": 100000, "indexation_allowed": False}},
            "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
            "cii": {"FY2024-25": 348, "FY2025-26": 360}
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_uc_flip_at_superstructure_exit():
    ctx = _ctx_basic_tax()

    # 20% at booking (m=0), 30% at super-structure (m=12), 50% at possession (m=24)
    ev_book = PaymentEvent(
        name="Booking 20%", stage="Booking",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
        percent_of_base=DD(20), gst_pct=DD(0), financeable_by_bank=False
    )
    ev_super = PaymentEvent(
        name="Super-Structure 30%", stage="SuperStructure",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=12),
        percent_of_base=DD(30), gst_pct=DD(0), financeable_by_bank=False
    )
    ev_poss = PaymentEvent(
        name="Possession 50%", stage="Possession",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=24),
        percent_of_base=DD(50), gst_pct=DD(0), financeable_by_bank=False
    )

    prop = Property(
        id="UC1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(20_000_000)),
        plan=PaymentPlan(events=[ev_book, ev_super, ev_poss]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),  # not moving in; investor flip
        exit=ExitSettings(sale_month=12, brokerage_pct=DD(1.0), buyer_tds_pct=DD(1.0))
    )

    person = Person(id="p1", name="Investor", tax_regime="old", shareholdings={}, liquid_cash=DD(6_000_000))
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=30,
        persons=[person], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"p1": DD(6_000_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf
    eng.run()
    res = eng.result()
    led = res["ledger"]

    # Validate a real end-to-end sale at month 12:
    assert any("Sale Proceeds (net of buyer TDS)" in l["label"] for l in led)
    assert any(l["label"] == "Brokerage on Sale" for l in led)
    # Realized XIRR should be computed (no fixed target, just presence)
    assert res["kpis"].get("realized_xirr") is not None
