from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy, ExitSettings
from rede.types import D as DD

def test_xirr_realized_and_mtm_basic():
    # Minimal portfolio: buy nothing (no demands), but set a sale month to trigger MTM/exit machinery
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(1_000_000))
    prop = Property(
        id="P1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(500_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        exit=ExitSettings(sale_month=3)  # small horizon
    )
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=6,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(1_000_000)},
        alt_invest_return_after_tax_pct=DD(6),
        loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )
    ctx = EngineContext()
    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf
    eng.run()
    k = eng.result()["kpis"]
    assert "mtm_xirr" in k and k["mtm_xirr"] is not None
    # realized_xirr may be None if not all cash flows are "realized" by horizon,
    # but after sale it should usually be present:
    assert "realized_xirr" in k
