import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.types import D as DD
from rede.models.core import (
    Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
)
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger,
    PropertyOwnershipCosts, PropertyOccupancy
)
from rede.models.enums import PaymentTriggerType

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_s10_assignment_pre_oc_exit_no_stamp_duty_if_supported():
    """
    Scenario: Investor books UC (10%), pays till plinth (25%), then assigns at m=20.
    Expect:
      - Assignment proceeds posted (or sale proceeds if engine uses unified label)
      - No stamp duty posting on assignment if modeled distinctly from conveyance
      - Realized XIRR should exist
    """
    ctx = _ctx()

    # Guard for assignment feature on Property (transfer/assignment events)
    if not hasattr(Property, "transfer_events"):
        pytest.skip("Assignment/transfer feature not exposed in this build")

    evs = [
        PaymentEvent(name="Booking 10%", stage="Booking",
                     trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
                     percent_of_base=DD(10), gst_pct=DD(0), financeable_by_bank=False),
        PaymentEvent(name="Plinth 15%", stage="Plinth",
                     trigger=PaymentTrigger(type=PaymentTriggerType.month, month=10),
                     percent_of_base=DD(15), gst_pct=DD(0), financeable_by_bank=False),
    ]
    prop = Property(
        id="ASSIGN1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(10_000_000)),
        plan=PaymentPlan(events=evs),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    # Assignment at m=20, markup 12%
    try:
        prop.transfer_events = [{"month": 20, "type": "assignment", "markup_pct": 12}]
    except Exception:
        pytest.skip("transfer_events field present but not assignable")

    p = Person(id="inv", name="Investor", tax_regime="old", shareholdings={}, liquid_cash=DD(2_000_000))
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=24,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"inv": DD(2_000_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
    res = eng.result()
    labs = [l["label"] for l in res["ledger"]]
    # Expect assignment/sale proceeds line
    assert any(("Assignment Proceeds" in x) or ("Sale Proceeds" in x) for x in labs), "Expected assignment/sale proceeds"
    # Stamp duty should NOT appear for assignment-only transfer (if modeled)
    if any("Assignment Proceeds" in x for x in labs):
        assert not any("Stamp Duty" in x for x in labs), "Stamp duty should not be charged for assignment"
    assert res["kpis"].get("realized_xirr") is not None
