#!/usr/bin/env python3
import argpar<PERSON>, json, sys
from pathlib import Path
from typing import Any, Dict

from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio

# Default tax config (safe baseline; can be overridden by --tax-json or env)
DEFAULT_TAX = {
    "tds_rules": {"rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}},
    "mf_tax": {
        "equity": {
            "holding_days_ltcg": 365,
            "stcg_rate_pct": 15,
            "ltcg_rate_pct": 10,
            "ltcg_annual_exemption": 100000,
            "indexation_allowed": False
        },
        "debt": {
            "holding_days_ltcg": 1095,
            "stcg_rate_pct": 30,
            "ltcg_rate_pct": 20,
            "ltcg_annual_exemption": 0,
            "indexation_allowed": False
        }
    },
    "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
    "cii": {
        "FY2019-20": 289, "FY2020-21": 301, "FY2021-22": 317, "FY2022-23": 331,
        "FY2023-24": 348, "FY2024-25": 348, "FY2025-26": 360
    }
}

class _Cfg:
    def __init__(self, content: Dict[str, Any]):
        self.content = content

def _load_json_if(path: str | None) -> Dict[str, Any] | None:
    if not path:
      return None
    p = Path(path)
    if not p.exists():
      return None
    try:
      return json.loads(p.read_text(encoding="utf-8"))
    except Exception:
      return None

def load_portfolio(path: str) -> Portfolio:
    data = json.loads(Path(path).read_text(encoding="utf-8"))
    return Portfolio.parse_obj(data)

def main():
    ap = argparse.ArgumentParser(description="Run Real Estate Engine on a JSON fixture.")
    ap.add_argument("fixture", help="Path to JSON fixture")
    ap.add_argument("-n", "--top-ledger", type=int, default=20, help="Number of ledger lines to show (text mode)")
    ap.add_argument("--json", action="store_true", help="Print compact JSON result (KPIs, flags, narratives[:10], top ledger)")
    ap.add_argument("--json-out", type=str, default="", help="Write full JSON result to file path")
    ap.add_argument("--tax-json", type=str, default="", help="Override default tax config with a JSON file")
    ap.add_argument("--full", action="store_true", help="Print full result JSON to stdout")
    args = ap.parse_args()

    # Portfolio
    try:
        pf = load_portfolio(args.fixture)
    except Exception as e:
        print(f"ERROR: failed to load portfolio fixture {args.fixture}: {e}", file=sys.stderr)
        sys.exit(2)

    # Tax config: try CLI -> ENV -> default
    tax_cfg = None
    if args.tax_json:
        tax_cfg = _load_json_if(args.tax_json)
    if not tax_cfg:
        from os import getenv
        tax_cfg = _load_json_if(getenv("TAX_LAWS_PATH", "")) or DEFAULT_TAX

    ctx = EngineContext()
    ctx.tax_laws_fy = _Cfg(tax_cfg)

    eng = TimelineEngine(ctx)
    eng.ctx.portfolio = pf

    try:
        eng.run()
        res = eng.result()
    except Exception as e:
        print(f"ERROR: engine run failed for {args.fixture}: {e}", file=sys.stderr)
        sys.exit(3)

    if args.json:
        compact = {
            "fixture": str(args.fixture),
            "kpis": res.get("kpis", {}),
            "risk_flags": res.get("risk_flags", []),
            "narrative_summary": (res.get("narrative_summary", [])[:10]),
            "ledger_top": (res.get("ledger", [])[:args.top_ledger]),
        }
        print(json.dumps(compact, ensure_ascii=False))
        return

    if args.json_out:
        Path(args.json_out).write_text(json.dumps(res, ensure_ascii=False, indent=2), encoding="utf-8")

    if args.full:
        print(json.dumps(res, ensure_ascii=False, indent=2))
        return

    # Human-readable text mode
    print("\n=== KPIs ===")
    for k, v in res.get("kpis", {}).items():
        print(f"{k:28s}: {v}")

    print("\n=== Risk Flags ===")
    rfs = res.get("risk_flags", [])
    if rfs:
        for rf in rfs:
            print(f"- {rf}")
    else:
        print("(none)")

    print("\n=== Narratives (first 10) ===")
    for s in res.get("narrative_summary", [])[:10]:
        print(f"- {s}")

    print(f"\n=== Ledger (top {args.top_ledger}) ===")
    for line in res.get("ledger", [])[:args.top_ledger]:
        print(f"{line['date']} | {line.get('property_id','-'):6s} | {line['label']:<40s} | {line['amount']:>12}")

if __name__ == "__main__":
    main()
