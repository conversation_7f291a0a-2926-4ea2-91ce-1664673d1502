import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.types import D as DD
from rede.models.core import (
    Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
)
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger,
    PropertyOwnershipCosts, PropertyOccupancy
)
from rede.models.enums import PaymentTriggerType

def _ctx():
    class _Cfg:
        content = {
            "cii": {"FY2024-25": 348, "FY2025-26": 360},
            # MF rules not needed here, keep minimal
        }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_s9_high_leverage_multi_hl_topups_risk_flags():
    """
    Scenario: UC CLP with large stage demands; liquidity tight; HL Top-ups used twice.
    Expect:
      - Funding waterfall uses HL Top-up (if allow_hl_topup=True)
      - Risk flag 'Relied on HL Top-up' present when used
      - OD utilisation flag may also appear under stress
    """
    ctx = _ctx()

    # Simple 10 / 40 / 50 plan to create two big shocks
    evs = [
        PaymentEvent(name="Booking 10%", stage="Booking",
                     trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
                     percent_of_base=DD(10), gst_pct=DD(0), financeable_by_bank=False),
        PaymentEvent(name="Structure 40%", stage="Structure",
                     trigger=PaymentTrigger(type=PaymentTriggerType.month, month=12),
                     percent_of_base=DD(40), gst_pct=DD(0), financeable_by_bank=True),
        PaymentEvent(name="Possession 50%", stage="Possession",
                     trigger=PaymentTrigger(type=PaymentTriggerType.month, month=24),
                     percent_of_base=DD(50), gst_pct=DD(0), financeable_by_bank=True),
    ]
    prop = Property(
        id="HLT", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(12_000_000), ltv_pct=DD(75)),
        plan=PaymentPlan(events=evs),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    person = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(800_000))

    # allow_hl_topup + small OD limit to force HL top-up usage
    fs = FundingStrategy(
        od_limit=DD(800_000), od_annual_rate_pct=DD(12),
        mf_fifo_enabled=False, allow_hl_topup=True
    )

    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=30,
        persons=[person], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(800_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=fs,
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )

    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf; eng.run()
    res = eng.result()
    flags = res.get("risk_flags", [])
    # If HL Top-up rung is implemented and used, risk flag must be present
    if any("HL Top-up" in (l.get("label","")) for l in res["ledger"]):
        assert any("Relied on HL Top-up" in f for f in flags), "Expected HL Top-up reliance flag when used"

    # OD utilisation ≥80% may also trigger under stress; assert if present
    if any(l for l in res["ledger"] if l.get("category") == "funding_od_interest"):
        assert any("OD utilisation reached ≥80%" in f for f in flags), "Expected OD ≥80% utilisation flag"
