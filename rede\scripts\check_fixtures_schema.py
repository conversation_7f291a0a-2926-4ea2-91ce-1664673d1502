#!/usr/bin/env python3
"""
check_fixtures_schema.py
Validate tests/fixtures/*.json against rede.models.core.Portfolio.

Features:
- Walk directory + glob (default: tests/fixtures/*.json)
- Parse each JSON with Portfolio (Pydantic)
- Report:
    OK / ERROR
    unknown fields (keys present in source but not in normalized model)
- Options:
    --dir DIR           (default: tests/fixtures)
    --pattern GLOB      (default: *.json)
    --write-normalized  (writes normalized JSON to artifacts/normalized/*.json)
    --strict            (non-zero exit if any warning OR error)
    --print-jsonschema  (print Portfolio JSON schema and exit)
    --summary-only      (only one-line summary)
"""
from __future__ import annotations
import argparse
import json
import sys
from pathlib import Path
from typing import Any, Dict, Tuple, List, Set

# --- Import your models
try:
    from rede.models.core import Portfolio
except Exception as e:
    print("ERROR: cannot import rede.models.core.Portfolio. Are you running from repo root?\n"
          f"Import error: {e}", file=sys.stderr)
    sys.exit(2)

# Try both Pydantic v1/v2 schema exporters
def _portfolio_json_schema() -> Dict[str, Any]:
    try:
        # pydantic v2
        return Portfolio.model_json_schema()  # type: ignore[attr-defined]
    except Exception:
        pass
    try:
        # pydantic v1
        return Portfolio.schema()  # type: ignore[attr-defined]
    except Exception as e:
        raise RuntimeError(f"Cannot produce JSON schema for Portfolio: {e}")

def _load_json(path: Path) -> Dict[str, Any]:
    return json.loads(path.read_text(encoding="utf-8"))

def _normalize_portfolio(data: Dict[str, Any]) -> Tuple[Dict[str, Any], Portfolio]:
    """
    Let Pydantic coerce/validate; return (normalized_dict, model)
    """
    model = Portfolio.parse_obj(data)
    try:
        # v2
        norm = model.model_dump()  # type: ignore[attr-defined]
    except Exception:
        # v1
        norm = model.dict()
    return norm, model

def _diff_unknown_keys(src: Any, norm: Any, prefix: str = "") -> List[str]:
    """
    Heuristic: report keys present in src but not found in normalized.
    Handles dict/list primitives recursively.
    """
    misses: List[str] = []
    if isinstance(src, dict) and isinstance(norm, dict):
        for k, v in src.items():
            if k not in norm:
                misses.append(prefix + k)
            else:
                misses.extend(_diff_unknown_keys(v, norm[k], prefix + f"{k}."))
    elif isinstance(src, list) and isinstance(norm, list):
        # align by index; if lengths differ, keys inside the overflow are missed
        L = min(len(src), len(norm))
        for i in range(L):
            misses.extend(_diff_unknown_keys(src[i], norm[i], prefix + f"[{i}]."))
        if len(src) > len(norm):
            for j in range(L, len(src)):
                # we cannot map fields inside overflow reliably, just mark index
                misses.append(prefix + f"[{j}]")
    # primitives: nothing to diff
    return misses

def validate_fixture(path: Path, write_normalized: bool, out_dir: Path | None) -> Tuple[bool, List[str], str]:
    """
    Returns (ok, warnings, error_text)
    ok == False if parse error
    warnings include unknown field paths
    error_text contains parse failure details (if any)
    """
    try:
        src = _load_json(path)
    except Exception as e:
        return False, [], f"Invalid JSON: {e}"

    try:
        norm, _ = _normalize_portfolio(src)
    except Exception as e:
        return False, [], f"Model validation failed: {e}"

    # Unknown keys (present in src but dropped in normalized)
    unknowns = _diff_unknown_keys(src, norm)

    if write_normalized and out_dir is not None:
        out_dir.mkdir(parents=True, exist_ok=True)
        (out_dir / path.name).write_text(json.dumps(norm, ensure_ascii=False, indent=2), encoding="utf-8")

    return True, unknowns, ""

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--dir", default="tests/fixtures", help="Directory containing JSON fixtures")
    ap.add_argument("--pattern", default="*.json", help="Glob pattern within dir")
    ap.add_argument("--write-normalized", action="store_true", help="Write normalized JSON files")
    ap.add_argument("--normalized-out", default="artifacts/normalized", help="Output dir for normalized JSON")
    ap.add_argument("--strict", action="store_true", help="Fail if warnings (unknown fields) exist")
    ap.add_argument("--print-jsonschema", action="store_true", help="Print Portfolio JSON schema and exit")
    ap.add_argument("--summary-only", action="store_true", help="Only print summary line")
    args = ap.parse_args()

    if args.print_jsonschema:
        schema = _portfolio_json_schema()
        print(json.dumps(schema, ensure_ascii=False, indent=2))
        return

    base = Path(args.dir)
    if not base.exists():
        print(f"ERROR: directory not found: {base}", file=sys.stderr)
        sys.exit(2)

    files = sorted(base.glob(args.pattern))
    if not files:
        print(f"No files matched {base}/{args.pattern}")
        return

    out_dir = Path(args.normalized_out) if args.write_normalized else None

    # Pretty colors
    BOLD = "\033[1m"; DIM = "\033[2m"; RED = "\033[31m"; YEL = "\033[33m"; GRN = "\033[32m"; NC = "\033[0m"

    if not args.summary_only:
        print(f"{BOLD}Checking fixtures in {base}/{args.pattern}{NC}")

    total = 0
    ok_count = 0
    warn_count = 0
    err_count = 0

    rows = []
    for f in files:
        total += 1
        ok, warns, err = validate_fixture(f, args.write_normalized, out_dir)
        if ok and not warns:
            status = f"{GRN}OK{NC}"
            ok_count += 1
        elif ok and warns:
            status = f"{YEL}WARN{NC}"
            warn_count += 1
        else:
            status = f"{RED}ERROR{NC}"
            err_count += 1

        rows.append((f.name, status, warns, err))

    # Print table
    if not args.summary_only:
        print(f"\n{BOLD}{'Fixture':36s} | {'Status':7s} | Details{NC}")
        print("-" * 90)
        for name, status, warns, err in rows:
            line = f"{name:36s} | {status:7s} | "
            if err:
                line += f"{RED}{err}{NC}"
            elif warns:
                line += f"{YEL}unknown fields: {', '.join(warns[:6])}{' ...' if len(warns) > 6 else ''}{NC}"
            else:
                line += "—"
            print(line)

    print(f"\n{BOLD}Summary:{NC} total={total}  ok={ok_count}  warn={warn_count}  error={err_count}")
    exit_code = 0
    if err_count > 0:
        exit_code = 3
    elif args.strict and warn_count > 0:
        exit_code = 4
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
