from decimal import Decimal as D
from rede.engine.loan_engine import LoanEngine
from rede.engine.context import EngineContext
from rede.models.property import Property, PropertyHeads
from rede.models.loans import TermLoan, LTVBasis, BankStageCap, RateIndex
from rede.types import D as DD, Money

def test_disbursal_min_ltv_stage_caps():
    # Agreement ex-GST base = 10,000,000; LTV 80% → headroom = 8,000,000
    heads = PropertyHeads(
        base_price_ex_gst=DD(10_000_000),
        plc_floor_rise=DD(0), parking_charges=DD(0),
        club_membership=DD(0), other_capital_heads=DD(0),
    )
    loan = TermLoan(
        id="L1", sanctioned_amount=DD(0), tenure_months=240,
        initial_roi_annual_pct=DD(9), spread_pct=DD(0), rate_index=RateIndex.FIXED,
        on_rate_change_policy="ADJUST_EMI",
        ltv_basis=LTVBasis(ltv_pct=DD(80.0)),
        rate_shocks=[]
    )
    prop = Property(
        id="P1", city="BLR", heads=heads,
        loan=loan, bank_stage_caps=[BankStageCap(stage_name="Plinth", max_pct_of_agreement_ex_gst=DD(10))],
    )

    eng = LoanEngine()
    # Financeable demand is 5,000,000, but stage cap is 10% of 10,000,000 = 1,000,000 ⇒ disbursal <= 1,000,000
    d1 = eng.compute_disbursal(EngineContext(), prop, "Plinth", Money(DD(5_000_000)), Money(DD(0)))
    assert D(d1) == D(1_000_000)

    # If bank has already paid 900,000, cap-remaining is 100,000
    d2 = eng.compute_disbursal(EngineContext(), prop, "Plinth", Money(DD(5_000_000)), Money(DD(900_000)))
    assert D(d2) == D(100_000)

    # If we change stage caps to 90%, LTV headroom (8,000,000) should bind
    prop.bank_stage_caps = [BankStageCap(stage_name="Plinth", max_pct_of_agreement_ex_gst=DD(90))]
    d3 = eng.compute_disbursal(EngineContext(), prop, "Plinth", Money(DD(50_000_000)), Money(DD(0)))
    assert D(d3) == D(8_000_000)
