#!/usr/bin/env bash
set -euo pipefail

# Load environment overrides if present (useful locally)
if [[ -f .env ]]; then
  # shellcheck disable=SC1091
  set -a; source .env; set +a
fi

export PYTHONPATH="${PYTHONPATH:-.}"

# ---- Config paths (used by your config loader) ----
: "${STATE_RULES_PATH:=configs/state_rules.json}"
: "${CITY_BENCHMARKS_PATH:=configs/city_benchmarks.json}"
: "${TAX_LAWS_PATH:=configs/tax_laws.json}"
: "${RATE_INDEX_PATH:=configs/rate_index.json}"
: "${BANK_POLICIES_PATH:=configs/bank_policies.json}"

export STATE_RULES_PATH CITY_BENCHMARKS_PATH TAX_LAWS_PATH RATE_INDEX_PATH BANK_POLICIES_PATH

# Warn (non-fatal) if any file is missing
for f in "$STATE_RULES_PATH" "$CITY_BENCHMARKS_PATH" "$TAX_LAWS_PATH" "$RATE_INDEX_PATH" "$BANK_POLICIES_PATH"; do
  if [[ ! -f "$f" ]]; then
    echo "WARN: config path not found: $f" >&2
  fi
done

# ---- Server env toggles ----
: "${CORS_ALLOW_ORIGINS:=*}"      # Harden for prod
: "${REDUCE_ERROR_TRACE:=0}"      # Set to 1 in prod to hide internal traces
export CORS_ALLOW_ORIGINS REDUCE_ERROR_TRACE

# ---- Network & hot reload ----
: "${HOST:=0.0.0.0}"
: "${PORT:=8000}"
: "${RELOAD:=1}"

# ---- Dependencies sanity check ----
if ! command -v uvicorn >/dev/null 2>&1; then
  echo "ERROR: uvicorn not found. Install via: pip install 'uvicorn[standard]' fastapi" >&2
  exit 1
fi

# ---- Launch ----
exec uvicorn server.main:app --host "$HOST" --port "$PORT" ${RELOAD:+--reload}
