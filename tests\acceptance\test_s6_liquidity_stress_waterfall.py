from decimal import Decimal as D
from rede.types import D as DD
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal, MFHoldingLot
from rede.models.property import Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger, PropertyOwnershipCosts, PropertyOccupancy
from rede.models.enums import PaymentTriggerType
from .helpers import run_engine

def test_s6_liquidity_stress_waterfall():
    u = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(100000))
    # Large builder payment at M18 to force shortfall; MF + OD + HL top-up may be needed
    events = [
        PaymentEvent(name="Small 10%", stage="Start", trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
                     percent_of_base=DD(10), gst_pct=DD(0), financeable_by_bank=False),
        PaymentEvent(name="Big 90%", stage="Possession", trigger=PaymentTrigger(type=PaymentTriggerType.month, month=18),
                     percent_of_base=DD(90), gst_pct=DD(0), financeable_by_bank=True)
    ]
    prop = Property(
        id="S", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(2_000_000), tds_buy_pct=DD(1), ltv_pct=DD(70)),
        plan=PaymentPlan(events=events),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    # MF stash that will be tapped first
    mf = MFHoldingLot(id="EQ1", scheme_name="EQ", asset_type="equity", units=500, nav_acquired=80, acquired_date="2023-01-01", current_nav=100, exit_load_pct=DD(1))
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=24,
        persons=[u], properties=[prop], mf_holdings=[mf],
        user_starting_liquid_cash_by_person={"u": DD(100000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(500000), od_annual_rate_pct=DD(12), mf_fifo_enabled=True, allow_hl_topup=True),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )
    res = run_engine(pf)
    ledger = res["ledger"]

    # Expect MF redemptions and possibly OD draw
    assert any(l for l in ledger if l["category"] == "funding_mf"), "Expected MF redemption in stress"
    # OD draw likely (depending on disbursal); allow either:
    # Check funding KPIs reflect stress
    k = res["kpis"]
    assert k["min_liquid_balance"] <= 0, "Liquidity should dip under stress"
    # Risk flags may include OD utilisation or HL Top-up (if used)
    assert any("OD utilisation reached ≥80%" in rf for rf in res["risk_flags"]), \
        "Expected OD utilisation ≥80% risk flag under stress"
