import pytest
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from rede.types import D as DD

def _ctx():
    class _Cfg: content = {"cii": {"FY2024-25": 348}}
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def test_od_sweep_reduces_interest_vs_plain_od():
    """
    Goal: OD/MaxGain account with sweep (surplus offsets principal)
    should incur LESS interest than plain OD without sweep.
    """
    ctx = _ctx()

    # Ensure funding_strategy has sweep toggle; else skip
    if not hasattr(FundingStrategy, "od_sweep_enabled"):
        pytest.skip("OD sweep feature not exposed in this build")

    # Minimal property (demand at m=6 forces OD usage)
    prop = Property(
        id="OD1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(3_000_000)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    person = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(800_000))

    # A) OD without sweep
    pfA = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
        persons=[person], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(800_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(2_000_000), od_annual_rate_pct=DD(12), mf_fifo_enabled=False, od_sweep_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )
    # B) OD with sweep enabled
    pfB = Portfolio.parse_obj({**pfA.dict(), "funding_strategy": {**pfA.funding_strategy.dict(), "od_sweep_enabled": True}})

    # Run both
    engA = TimelineEngine(ctx); engA.ctx.portfolio = pfA; engA.run(); resA = engA.result()
    engB = TimelineEngine(ctx); engB.ctx.portfolio = pfB; engB.run(); resB = engB.result()

    odA = sum(l["amount"] for l in resA["ledger"] if l.get("category") == "funding_od_interest")
    odB = sum(l["amount"] for l in resB["ledger"] if l.get("category") == "funding_od_interest")

    assert odB <= odA, "OD with sweep should never incur more interest than plain OD"
